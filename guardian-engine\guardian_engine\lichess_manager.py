"""
Lichess Manager for Guardian Chess Tutor Backend.

Handles communication with the Lichess API using Personal Access Token authentication.
"""

import logging
import berserk
import threading
import time
import chess
from typing import Optional, Dict, Any, Callable
from .tutor_brain import TutorBrain

logger = logging.getLogger(__name__)

class LichessManagerError(Exception):
    """Custom exception for Lichess Manager errors."""
    pass

class LichessManager:
    """
    Manages communication with the Lichess API.
    
    This class handles:
    - Personal Access Token authentication
    - API client initialization
    - Account information retrieval
    - Error handling and logging
    """
    
    def __init__(self, pat_token: Optional[str] = None):
        """
        Initialize Lichess Manager.

        Args:
            pat_token: Personal Access Token for Lichess API authentication
        """
        self.pat_token = pat_token
        self.client: Optional[berserk.Client] = None
        self.account_info: Optional[Dict[str, Any]] = None

        # Event streaming attributes
        self.event_stream_thread: Optional[threading.Thread] = None
        self.event_stream_running = False
        self.event_stream_stop_event = threading.Event()
        self.game_start_callback: Optional[Callable[[str], None]] = None

        # Game streaming attributes
        self.active_game_streams: Dict[str, threading.Thread] = {}
        self.game_stream_stop_events: Dict[str, threading.Event] = {}
        self.uci_manager = None  # Will be set by the app

        # Tutor Brain for intelligent analysis
        self.tutor_brain = TutorBrain()

        # Store pre-move analysis for blunder detection
        self.pre_move_analysis: Dict[str, Dict[str, Any]] = {}

        logger.info("Initializing Lichess Manager")

        if self.pat_token:
            self._initialize_client()
        else:
            logger.warning("No PAT token provided - Lichess functionality will be limited")

    def set_uci_manager(self, uci_manager):
        """
        Set the UCI manager for direct analysis.

        Args:
            uci_manager: The UCI manager instance for chess analysis
        """
        self.uci_manager = uci_manager
        logger.info("UCI manager set for direct analysis")
    
    def _initialize_client(self):
        """Initialize the berserk client with authentication."""
        try:
            # Create authenticated session
            session = berserk.TokenSession(self.pat_token)
            
            # Initialize client
            self.client = berserk.Client(session=session)
            
            logger.info("Lichess client initialized successfully")
            
            # Test the connection by fetching account info
            self._fetch_account_info()
            
        except Exception as e:
            logger.error(f"Failed to initialize Lichess client: {e}")
            raise LichessManagerError(f"Client initialization failed: {e}")
    
    def _fetch_account_info(self):
        """Fetch and cache account information."""
        try:
            if not self.client:
                raise LichessManagerError("Client not initialized")
            
            # Fetch account information
            self.account_info = self.client.account.get()
            
            username = self.account_info.get('username', 'Unknown')
            logger.info(f"Successfully authenticated as Lichess user: {username}")
            
        except Exception as e:
            logger.error(f"Failed to fetch account info: {e}")
            raise LichessManagerError(f"Account info fetch failed: {e}")
    
    def is_authenticated(self) -> bool:
        """
        Check if the client is properly authenticated.
        
        Returns:
            True if authenticated, False otherwise
        """
        return self.client is not None and self.account_info is not None
    
    def get_account_info(self) -> Dict[str, Any]:
        """
        Get cached account information.
        
        Returns:
            Dictionary containing account information
            
        Raises:
            LichessManagerError: If not authenticated or account info unavailable
        """
        if not self.is_authenticated():
            raise LichessManagerError("Not authenticated with Lichess")
        
        if not self.account_info:
            raise LichessManagerError("Account information not available")
        
        return self.account_info.copy()
    
    def refresh_account_info(self) -> Dict[str, Any]:
        """
        Refresh account information from Lichess API.
        
        Returns:
            Updated account information
            
        Raises:
            LichessManagerError: If refresh fails
        """
        if not self.client:
            raise LichessManagerError("Client not initialized")
        
        try:
            self._fetch_account_info()
            return self.get_account_info()
            
        except Exception as e:
            logger.error(f"Failed to refresh account info: {e}")
            raise LichessManagerError(f"Account refresh failed: {e}")
    
    def get_user_status(self) -> Dict[str, Any]:
        """
        Get comprehensive user status information.
        
        Returns:
            Dictionary containing user status and capabilities
        """
        if not self.is_authenticated():
            return {
                'authenticated': False,
                'error': 'Not authenticated with Lichess'
            }
        
        try:
            account = self.get_account_info()
            
            return {
                'authenticated': True,
                'username': account.get('username'),
                'title': account.get('title'),
                'online': account.get('online', False),
                'patron': account.get('patron', False),
                'verified': account.get('verified', False),
                'profile': {
                    'country': account.get('profile', {}).get('country'),
                    'location': account.get('profile', {}).get('location'),
                    'bio': account.get('profile', {}).get('bio'),
                },
                'perfs': self._format_performance_ratings(account.get('perfs', {})),
                'created_at': account.get('createdAt'),
                'seen_at': account.get('seenAt')
            }
            
        except Exception as e:
            logger.error(f"Failed to get user status: {e}")
            return {
                'authenticated': False,
                'error': f'Failed to retrieve user status: {str(e)}'
            }
    
    def _format_performance_ratings(self, perfs: Dict[str, Any]) -> Dict[str, Any]:
        """
        Format performance ratings for display.
        
        Args:
            perfs: Raw performance data from Lichess
            
        Returns:
            Formatted performance ratings
        """
        formatted_perfs = {}
        
        # Common time controls
        time_controls = ['bullet', 'blitz', 'rapid', 'classical', 'correspondence']
        
        for tc in time_controls:
            if tc in perfs:
                perf_data = perfs[tc]
                formatted_perfs[tc] = {
                    'rating': perf_data.get('rating'),
                    'rd': perf_data.get('rd'),  # Rating deviation
                    'games': perf_data.get('games'),
                    'provisional': perf_data.get('prov', False)
                }
        
        return formatted_perfs
    
    def test_connection(self) -> Dict[str, Any]:
        """
        Test the connection to Lichess API.
        
        Returns:
            Dictionary containing connection test results
        """
        try:
            if not self.pat_token:
                return {
                    'success': False,
                    'error': 'No PAT token configured'
                }
            
            if not self.client:
                self._initialize_client()
            
            # Test by fetching account info
            account = self.refresh_account_info()
            
            return {
                'success': True,
                'username': account.get('username'),
                'message': 'Successfully connected to Lichess API'
            }
            
        except Exception as e:
            logger.error(f"Connection test failed: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def start_event_stream(self, game_start_callback: Optional[Callable[[str], None]] = None):
        """
        Start the Lichess event stream in a background thread.

        Args:
            game_start_callback: Optional callback function to call when a game starts.
                                Will be called with the game ID as argument.
        """
        if not self.is_authenticated():
            logger.error("Cannot start event stream: not authenticated with Lichess")
            return False

        if self.event_stream_running:
            logger.warning("Event stream is already running")
            return True

        self.game_start_callback = game_start_callback
        self.event_stream_stop_event.clear()

        # Start the event stream in a background thread
        self.event_stream_thread = threading.Thread(
            target=self._event_stream_worker,
            name="LichessEventStream",
            daemon=True
        )

        self.event_stream_thread.start()
        logger.info("Lichess event stream started in background thread")
        return True

    def stop_event_stream(self):
        """Stop the Lichess event stream and all game streams."""
        if not self.event_stream_running:
            logger.info("Event stream is not running")
            return

        logger.info("Stopping Lichess event stream...")

        # Stop all active game streams first
        self.stop_all_game_streams()

        # Stop the main event stream
        self.event_stream_stop_event.set()

        if self.event_stream_thread and self.event_stream_thread.is_alive():
            self.event_stream_thread.join(timeout=5.0)

        self.event_stream_running = False
        logger.info("Lichess event stream stopped")

    def _event_stream_worker(self):
        """
        Background worker that maintains the Lichess event stream.

        This method runs in a separate thread and continuously listens for events
        from the Lichess API. It handles reconnection on failures and processes
        gameStart events.
        """
        self.event_stream_running = True
        reconnect_delay = 1  # Start with 1 second delay
        max_reconnect_delay = 60  # Maximum 60 seconds between reconnection attempts

        logger.info("Event stream worker started")

        while not self.event_stream_stop_event.is_set():
            try:
                logger.info("Connecting to Lichess event stream...")

                # Connect to the event stream
                events = self.client.board.stream_incoming_events()

                logger.info("✅ Connected to Lichess event stream - listening for events...")
                reconnect_delay = 1  # Reset delay on successful connection

                # Process events
                for event in events:
                    if self.event_stream_stop_event.is_set():
                        break

                    self._process_event(event)

                # If we reach here, the stream ended normally
                logger.warning("Event stream ended normally")

            except Exception as e:
                if self.event_stream_stop_event.is_set():
                    break

                logger.error(f"Event stream error: {e}")
                logger.info(f"Reconnecting in {reconnect_delay} seconds...")

                # Wait before reconnecting (with stop event check)
                if self.event_stream_stop_event.wait(timeout=reconnect_delay):
                    break

                # Exponential backoff with maximum delay
                reconnect_delay = min(reconnect_delay * 2, max_reconnect_delay)

        self.event_stream_running = False
        logger.info("Event stream worker stopped")

    def _process_event(self, event: Dict[str, Any]):
        """
        Process a single event from the Lichess event stream.

        Args:
            event: Event data from Lichess API
        """
        try:
            event_type = event.get('type')

            if event_type == 'gameStart':
                self._handle_game_start_event(event)
            elif event_type == 'gameFinish':
                self._handle_game_finish_event(event)
            elif event_type == 'challenge':
                self._handle_challenge_event(event)
            elif event_type == 'challengeCanceled':
                self._handle_challenge_canceled_event(event)
            elif event_type == 'challengeDeclined':
                self._handle_challenge_declined_event(event)
            else:
                logger.debug(f"Received unhandled event type: {event_type}")

        except Exception as e:
            logger.error(f"Error processing event: {e}")
            logger.debug(f"Event data: {event}")

    def _handle_game_start_event(self, event: Dict[str, Any]):
        """
        Handle a gameStart event.

        Args:
            event: gameStart event data
        """
        try:
            game_data = event.get('game', {})
            game_id = game_data.get('id')

            if not game_id:
                logger.warning("gameStart event missing game ID")
                return

            # Extract additional game information
            opponent = game_data.get('opponent', {})
            opponent_name = opponent.get('username', 'Unknown')
            color = game_data.get('color', 'unknown')
            time_control = game_data.get('speed', 'unknown')
            rated = game_data.get('rated', False)

            logger.info(f"🎮 New game started! Game ID: {game_id}")
            logger.info(f"   Opponent: {opponent_name}")
            logger.info(f"   Playing as: {color}")
            logger.info(f"   Time control: {time_control}")
            logger.info(f"   Rated: {'Yes' if rated else 'No'}")

            # Start real-time game streaming
            self._start_game_stream(game_id)

            # Call the callback if provided
            if self.game_start_callback:
                try:
                    self.game_start_callback(game_id)
                except Exception as e:
                    logger.error(f"Error in game start callback: {e}")

        except Exception as e:
            logger.error(f"Error handling gameStart event: {e}")
            logger.debug(f"Event data: {event}")

    def _handle_game_finish_event(self, event: Dict[str, Any]):
        """Handle a gameFinish event."""
        try:
            game_data = event.get('game', {})
            game_id = game_data.get('id')

            if game_id:
                logger.info(f"🏁 Game finished: {game_id}")
                # Stop the game stream for this game
                self._stop_game_stream(game_id)

        except Exception as e:
            logger.error(f"Error handling gameFinish event: {e}")

    def _handle_challenge_event(self, event: Dict[str, Any]):
        """Handle a challenge event."""
        try:
            challenge_data = event.get('challenge', {})
            challenger = challenge_data.get('challenger', {}).get('name', 'Unknown')

            logger.info(f"⚔️  Challenge received from: {challenger}")

        except Exception as e:
            logger.error(f"Error handling challenge event: {e}")

    def _handle_challenge_canceled_event(self, event: Dict[str, Any]):
        """Handle a challengeCanceled event."""
        logger.debug("Challenge canceled")

    def _handle_challenge_declined_event(self, event: Dict[str, Any]):
        """Handle a challengeDeclined event."""
        logger.debug("Challenge declined")

    def get_event_stream_status(self) -> Dict[str, Any]:
        """
        Get the current status of the event stream.

        Returns:
            Dictionary containing event stream status information
        """
        return {
            'running': self.event_stream_running,
            'thread_alive': self.event_stream_thread.is_alive() if self.event_stream_thread else False,
            'authenticated': self.is_authenticated(),
            'callback_configured': self.game_start_callback is not None,
            'active_game_streams': len(self.active_game_streams)
        }

    def _start_game_stream(self, game_id: str):
        """
        Start streaming a specific game in a background thread.

        Args:
            game_id: The ID of the game to stream
        """
        if game_id in self.active_game_streams:
            logger.warning(f"Game stream for {game_id} is already running")
            return

        logger.info(f"🔴 Starting real-time stream for game: {game_id}")

        # Create stop event for this game
        stop_event = threading.Event()
        self.game_stream_stop_events[game_id] = stop_event

        # Start the game stream in a background thread
        game_thread = threading.Thread(
            target=self._game_stream_worker,
            args=(game_id, stop_event),
            name=f"GameStream-{game_id}",
            daemon=True
        )

        self.active_game_streams[game_id] = game_thread
        game_thread.start()

        logger.info(f"✅ Game stream thread started for: {game_id}")

    def _stop_game_stream(self, game_id: str):
        """
        Stop streaming a specific game.

        Args:
            game_id: The ID of the game to stop streaming
        """
        if game_id not in self.active_game_streams:
            logger.debug(f"No active stream found for game: {game_id}")
            return

        logger.info(f"🛑 Stopping game stream for: {game_id}")

        # Signal the thread to stop
        if game_id in self.game_stream_stop_events:
            self.game_stream_stop_events[game_id].set()

        # Wait for thread to finish
        thread = self.active_game_streams[game_id]
        if thread.is_alive():
            thread.join(timeout=3.0)

        # Clean up
        self.active_game_streams.pop(game_id, None)
        self.game_stream_stop_events.pop(game_id, None)
        self.pre_move_analysis.pop(game_id, None)  # Clean up blunder detection data

        logger.info(f"✅ Game stream stopped for: {game_id}")

    def _game_stream_worker(self, game_id: str, stop_event: threading.Event):
        """
        Background worker that streams a specific game and analyzes positions.

        Args:
            game_id: The ID of the game to stream
            stop_event: Event to signal when to stop streaming
        """
        logger.info(f"Game stream worker started for: {game_id}")

        try:
            # Connect to the game stream
            game_stream = self.client.board.stream_game_state(game_id)

            logger.info(f"📡 Connected to game stream: {game_id}")

            board = None
            move_count = 0

            # Process game events
            for event in game_stream:
                if stop_event.is_set():
                    break

                event_type = event.get('type')

                if event_type == 'gameFull':
                    # Initialize board state from gameFull
                    board, initial_move_count = self._initialize_board_from_game_full(game_id, event)
                    if board:
                        move_count = initial_move_count  # Set move count to number of moves already applied
                        # Analyze initial position
                        self._analyze_position(game_id, board, move_count, "Initial position")

                elif event_type == 'gameState':
                    # Process move updates
                    if board:
                        new_move = self._process_game_state_update(game_id, board, event, move_count)
                        if new_move:
                            move_count += 1
                            # Analyze position after the move
                            self._analyze_position(game_id, board, move_count, new_move)

                elif event_type == 'chatLine':
                    # Handle chat messages (optional)
                    self._handle_chat_message(game_id, event)

                else:
                    logger.debug(f"[{game_id}] Unhandled game event type: {event_type}")

            logger.info(f"📡 Game stream ended for: {game_id}")

        except Exception as e:
            logger.error(f"Error in game stream worker for {game_id}: {e}")

        finally:
            # Clean up when stream ends
            self.active_game_streams.pop(game_id, None)
            self.game_stream_stop_events.pop(game_id, None)
            self.pre_move_analysis.pop(game_id, None)  # Clean up blunder detection data
            logger.info(f"Game stream worker stopped for: {game_id}")

    def _initialize_board_from_game_full(self, game_id: str, game_full_event: Dict[str, Any]) -> tuple[Optional[chess.Board], int]:
        """
        Initialize a chess board from a gameFull event.

        Args:
            game_id: The game ID
            game_full_event: The gameFull event data

        Returns:
            Tuple of (Initialized chess.Board instance or None if failed, number of moves applied)
        """
        try:
            # Extract initial FEN from gameFull
            initial_fen = game_full_event.get('initialFen', 'rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1')

            # Handle special case where Lichess sends 'startpos' instead of FEN
            if initial_fen == 'startpos':
                initial_fen = 'rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1'

            # Create board from FEN
            board = chess.Board(initial_fen)

            # Apply any existing moves
            moves_string = game_full_event.get('state', {}).get('moves', '')
            move_count = 0
            if moves_string:
                moves = moves_string.split()
                for move_uci in moves:
                    if move_uci:  # Skip empty strings
                        try:
                            board.push_uci(move_uci)
                            move_count += 1
                        except ValueError as e:
                            logger.error(f"[{game_id}] Invalid move in initial state: {move_uci} - {e}")
                            return None, 0

            logger.info(f"[{game_id}] 🏁 Board initialized with {move_count} moves")
            logger.info(f"[{game_id}] 📋 Current FEN: {board.fen()}")

            return board, move_count

        except Exception as e:
            logger.error(f"[{game_id}] Failed to initialize board from gameFull: {e}")
            return None, 0

    def _process_game_state_update(self, game_id: str, board: chess.Board, game_state_event: Dict[str, Any], current_move_count: int) -> Optional[str]:
        """
        Process a gameState update and apply new moves to the board.
        Also analyzes user moves for blunders using pre-move analysis.

        Args:
            game_id: The game ID
            board: The current board state
            game_state_event: The gameState event data
            current_move_count: Current number of moves processed

        Returns:
            The new move in UCI format, or None if no new move
        """
        try:
            # Get the moves string
            moves_string = game_state_event.get('moves', '')
            if not moves_string:
                return None

            moves = moves_string.split()

            # Check if there are new moves
            if len(moves) <= current_move_count:
                return None

            # Apply new moves and analyze for blunders
            new_moves = moves[current_move_count:]
            latest_move = None

            for move_uci in new_moves:
                if move_uci:  # Skip empty strings
                    try:
                        # 🧠 BLUNDER DETECTION: Analyze move before applying it
                        if game_id in self.pre_move_analysis:
                            self._analyze_user_move_for_blunder(game_id, move_uci)

                        board.push_uci(move_uci)
                        latest_move = move_uci
                        logger.info(f"[{game_id}] 🔄 Move applied: {move_uci}")
                    except ValueError as e:
                        logger.error(f"[{game_id}] Invalid move: {move_uci} - {e}")
                        return None

            return latest_move

        except Exception as e:
            logger.error(f"[{game_id}] Error processing game state update: {e}")
            return None

    def _analyze_position(self, game_id: str, board: chess.Board, move_number: int, move_description: str):
        """
        Analyze the current position using the Guardian analysis engine.

        Args:
            game_id: The game ID
            board: The current board state
            move_number: The current move number
            move_description: Description of the move or position
        """
        try:
            if not self.uci_manager:
                logger.warning(f"[{game_id}] No UCI manager available for analysis")
                return

            current_fen = board.fen()

            logger.info(f"[{game_id}] 🧠 Analyzing position after move {move_number}: {move_description}")

            # Use UCI manager directly for analysis
            analysis_result = self.uci_manager.analyze_position(
                fen=current_fen,
                time_limit=2.0,  # 2 seconds analysis time
                multi_pv=3      # Top 3 moves
            )

            if analysis_result:
                best_move = analysis_result.get('bestMove')
                evaluation = analysis_result.get('evaluation', {})

                # Log the analysis result
                eval_str = evaluation.get('description', 'N/A') if evaluation else 'N/A'
                logger.info(f"[{game_id}] 📊 Analysis complete. Best move: {best_move}, Eval: {eval_str}")

                # Log top variations if available
                analysis_list = analysis_result.get('analysis', [])
                if len(analysis_list) > 1:
                    logger.info(f"[{game_id}] 🔍 Top variations:")
                    for i, variation in enumerate(analysis_list[:3]):
                        move = variation.get('move')
                        var_eval = variation.get('evaluation', {})
                        var_eval_str = var_eval.get('description', 'N/A') if var_eval else 'N/A'
                        logger.info(f"[{game_id}]   {i+1}. {move} ({var_eval_str})")

                # 🧠 TUTOR BRAIN: Analyze for critical moments
                self._analyze_with_tutor_brain(game_id, analysis_result)

                # Store analysis for blunder detection on next move
                self.pre_move_analysis[game_id] = analysis_result
            else:
                logger.warning(f"[{game_id}] Analysis returned no results")

        except Exception as e:
            logger.error(f"[{game_id}] Error analyzing position: {e}")

    def _analyze_with_tutor_brain(self, game_id: str, analysis_result: Dict[str, Any]):
        """
        Analyze position with Tutor Brain for critical moments and learning opportunities.

        Args:
            game_id: The game ID
            analysis_result: Analysis result from UCI manager
        """
        try:
            # Check if this is a critical moment
            is_critical = self.tutor_brain.is_critical_moment(analysis_result)

            if is_critical:
                # Get detailed critical moment analysis
                critical_analysis = self.tutor_brain.analyze_critical_moment(analysis_result)

                if critical_analysis:
                    gap = critical_analysis['evaluation_gap']
                    moment_type = critical_analysis['type']
                    best_move = critical_analysis['best_move']['move']
                    advice = critical_analysis['advice']

                    logger.info(f"[{game_id}] 🧠 TUTOR BRAIN: CRITICAL MOMENT DETECTED!")
                    logger.info(f"[{game_id}] 🎯 Type: {moment_type.replace('_', ' ').title()}")
                    logger.info(f"[{game_id}] ⚡ Evaluation gap: {gap} centipawns ({gap/100:.1f} pawns)")
                    logger.info(f"[{game_id}] 🎓 Best move: {best_move}")
                    logger.info(f"[{game_id}] 💡 Advice: {advice}")
                else:
                    logger.info(f"[{game_id}] 🧠 TUTOR BRAIN: CRITICAL MOMENT DETECTED! A tactical opportunity is available.")
            else:
                logger.info(f"[{game_id}] 🧠 TUTOR BRAIN: Position is quiet.")

        except Exception as e:
            logger.error(f"[{game_id}] Error in Tutor Brain analysis: {e}")

    def _analyze_user_move_for_blunder(self, game_id: str, user_move_uci: str):
        """
        Analyze a user's move for blunders using pre-move analysis.

        Args:
            game_id: The game ID
            user_move_uci: The move the user played in UCI format
        """
        try:
            pre_move_analysis = self.pre_move_analysis.get(game_id)
            if not pre_move_analysis:
                logger.debug(f"[{game_id}] No pre-move analysis available for blunder detection")
                return

            # Analyze the user's move
            blunder_result = self.tutor_brain.analyze_user_move(pre_move_analysis, user_move_uci)

            if blunder_result.get('is_blunder'):
                # This is a blunder!
                cpl = blunder_result['centipawn_loss']
                best_move = blunder_result['best_move']
                severity = blunder_result.get('severity', 'mistake')
                advice = blunder_result.get('advice', '')
                user_move_found = blunder_result.get('user_move_found', False)
                is_critical = blunder_result.get('is_critical_blunder', False)
                state_before = blunder_result.get('state_before', 'unknown')
                state_after = blunder_result.get('state_after', 'unknown')

                if is_critical:
                    logger.info(f"[{game_id}] 🧠 TUTOR BRAIN: CRITICAL BLUNDER DETECTED!")
                    logger.info(f"[{game_id}] ⚠️  Game state change: {state_before} → {state_after}")
                else:
                    logger.info(f"[{game_id}] 🧠 TUTOR BRAIN: BLUNDER DETECTED!")
                    logger.info(f"[{game_id}] ℹ️  Game state unchanged: {state_after}")

                logger.info(f"[{game_id}] 💥 Severity: {severity.replace('_', ' ').title()}")
                logger.info(f"[{game_id}] 📉 Centipawn Loss: {cpl} ({cpl/100:.1f} pawns)")
                logger.info(f"[{game_id}] 🎯 Best move was: {best_move}")
                logger.info(f"[{game_id}] 🔍 User move in top variations: {'Yes' if user_move_found else 'No'}")
                logger.info(f"[{game_id}] 💡 Contextual advice: {advice}")
            else:
                # Good move or no blunder detected
                if 'error' not in blunder_result:
                    cpl = blunder_result.get('centipawn_loss', 0)
                    state_after = blunder_result.get('state_after', 'unknown')
                    if cpl > 0:
                        logger.info(f"[{game_id}] 🧠 TUTOR BRAIN: Decent move. CPL: {cpl} ({cpl/100:.1f} pawns), position: {state_after}")
                    else:
                        logger.info(f"[{game_id}] 🧠 TUTOR BRAIN: Solid move. Position: {state_after}")

        except Exception as e:
            logger.error(f"[{game_id}] Error in blunder analysis: {e}")

    def _handle_chat_message(self, game_id: str, chat_event: Dict[str, Any]):
        """
        Handle chat messages in the game.

        Args:
            game_id: The game ID
            chat_event: The chat event data
        """
        try:
            username = chat_event.get('username', 'Unknown')
            text = chat_event.get('text', '')
            room = chat_event.get('room', 'player')

            logger.debug(f"[{game_id}] 💬 Chat [{room}] {username}: {text}")

        except Exception as e:
            logger.error(f"[{game_id}] Error handling chat message: {e}")

    def stop_all_game_streams(self):
        """Stop all active game streams."""
        game_ids = list(self.active_game_streams.keys())
        for game_id in game_ids:
            self._stop_game_stream(game_id)

        logger.info(f"Stopped {len(game_ids)} game streams")
