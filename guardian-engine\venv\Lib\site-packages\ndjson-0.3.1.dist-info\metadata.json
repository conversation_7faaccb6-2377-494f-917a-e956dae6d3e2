{"classifiers": ["Development Status :: 2 - Pre-Alpha", "Intended Audience :: <PERSON><PERSON><PERSON>", "License :: OSI Approved :: GNU General Public License v3 (GPLv3)", "Natural Language :: English", "Programming Language :: Python :: 2", "Programming Language :: Python :: 2.7", "Programming Language :: Python :: 3", "Programming Language :: Python :: 3.5", "Programming Language :: Python :: 3.6", "Programming Language :: Python :: 3.7"], "extensions": {"python.details": {"contacts": [{"email": "<EMAIL>", "name": "<PERSON>", "role": "author"}], "document_names": {"description": "DESCRIPTION.rst"}, "project_urls": {"Home": "https://github.com/rhgrant10/ndjson"}}}, "generator": "bdist_wheel (0.30.0)", "keywords": ["nd<PERSON><PERSON>"], "license": "GNU General Public License v3", "metadata_version": "2.0", "name": "nd<PERSON><PERSON>", "summary": "JsonDecoder for ndjson", "test_requires": [{"requires": ["pytest", "six"]}], "version": "0.3.1"}