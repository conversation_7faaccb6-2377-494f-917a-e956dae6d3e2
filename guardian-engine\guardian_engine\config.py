"""
Configuration module for Guardian Chess Tutor Backend.

Handles environment-based configuration for different deployment scenarios.
"""

import os
from pathlib import Path

def _get_default_stockfish_path():
    """Get default Stockfish path based on operating system."""
    import platform

    system = platform.system().lower()

    if system == 'windows':
        # Common Windows installation paths
        possible_paths = [
            'C:\\Program Files\\Stockfish\\stockfish.exe',
            'C:\\Program Files (x86)\\Stockfish\\stockfish.exe',
            'stockfish.exe'  # If in PATH
        ]
    elif system == 'darwin':  # macOS
        possible_paths = [
            '/usr/local/bin/stockfish',  # Homebrew
            '/opt/homebrew/bin/stockfish',  # Apple Silicon Homebrew
            'stockfish'  # If in PATH
        ]
    else:  # Linux and others
        possible_paths = [
            '/usr/bin/stockfish',
            '/usr/local/bin/stockfish',
            'stockfish'  # If in PATH
        ]

    # Check which path exists
    for path in possible_paths:
        if Path(path).exists() or path in ['stockfish', 'stockfish.exe']:
            return path

    # If none found, return the first option as default
    return possible_paths[0]

class Config:
    """Base configuration class."""

    # Flask configuration
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key-change-in-production'

    # Stockfish configuration
    STOCKFISH_PATH = os.environ.get('STOCKFISH_PATH') or _get_default_stockfish_path()
    
    # UCI configuration
    UCI_TIMEOUT = int(os.environ.get('UCI_TIMEOUT', 30))  # seconds

    # Lichess API configuration
    LICHESS_PAT = os.environ.get('LICHESS_PAT')  # Personal Access Token

    # Tutor Brain configuration
    TACTIC_THRESHOLD = int(os.environ.get('TACTIC_THRESHOLD', 150))  # centipawns
    BLUNDER_THRESHOLD = int(os.environ.get('BLUNDER_THRESHOLD', 200))  # centipawns

    # Game state thresholds (in pawns)
    EQUAL_STATE_THRESHOLD = float(os.environ.get('EQUAL_STATE_THRESHOLD', 0.5))  # pawns
    WINNING_STATE_THRESHOLD = float(os.environ.get('WINNING_STATE_THRESHOLD', 2.0))  # pawns

    # Logging configuration
    LOG_LEVEL = os.environ.get('LOG_LEVEL', 'INFO')


class DevelopmentConfig(Config):
    """Development configuration."""
    DEBUG = True
    FLASK_ENV = 'development'

class ProductionConfig(Config):
    """Production configuration."""
    DEBUG = False
    FLASK_ENV = 'production'

class TestingConfig(Config):
    """Testing configuration."""
    TESTING = True
    DEBUG = True

# Configuration dictionary
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}
