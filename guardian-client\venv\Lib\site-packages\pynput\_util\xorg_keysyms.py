# coding: utf-8
# pynput
# Copyright (C) 2015-2024 <PERSON>
#
# This program is free software: you can redistribute it and/or modify it under
# the terms of the GNU Lesser General Public License as published by the Free
# Software Foundation, either version 3 of the License, or (at your option) any
# later version.
#
# This program is distributed in the hope that it will be useful, but WITHOUT
# ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS
# FOR A PARTICULAR PURPOSE. See the GNU Lesser General Public License for more
# details.
#
# You should have received a copy of the GNU Lesser General Public License
# along with this program. If not, see <http://www.gnu.org/licenses/>.

# pylint: disable=C0111,C0302

SYMBOLS = {
    '0': (0x0030, u'\u0030'),
    '1': (0x0031, u'\u0031'),
    '2': (0x0032, u'\u0032'),
    '3': (0x0033, u'\u0033'),
    '4': (0x0034, u'\u0034'),
    '5': (0x0035, u'\u0035'),
    '6': (0x0036, u'\u0036'),
    '7': (0x0037, u'\u0037'),
    '8': (0x0038, u'\u0038'),
    '9': (0x0039, u'\u0039'),
    'A': (0x0041, u'\u0041'),
    'AE': (0x00c6, u'\u00C6'),
    'Aacute': (0x00c1, u'\u00C1'),
    'Abelowdot': (0x1001ea0, u'\u1EA0'),
    'Abreve': (0x01c3, u'\u0102'),
    'Abreveacute': (0x1001eae, u'\u1EAE'),
    'Abrevebelowdot': (0x1001eb6, u'\u1EB6'),
    'Abrevegrave': (0x1001eb0, u'\u1EB0'),
    'Abrevehook': (0x1001eb2, u'\u1EB2'),
    'Abrevetilde': (0x1001eb4, u'\u1EB4'),
    'Acircumflex': (0x00c2, u'\u00C2'),
    'Acircumflexacute': (0x1001ea4, u'\u1EA4'),
    'Acircumflexbelowdot': (0x1001eac, u'\u1EAC'),
    'Acircumflexgrave': (0x1001ea6, u'\u1EA6'),
    'Acircumflexhook': (0x1001ea8, u'\u1EA8'),
    'Acircumflextilde': (0x1001eaa, u'\u1EAA'),
    'Adiaeresis': (0x00c4, u'\u00C4'),
    'Agrave': (0x00c0, u'\u00C0'),
    'Ahook': (0x1001ea2, u'\u1EA2'),
    'Amacron': (0x03c0, u'\u0100'),
    'Aogonek': (0x01a1, u'\u0104'),
    'Arabic_0': (0x1000660, u'\u0660'),
    'Arabic_1': (0x1000661, u'\u0661'),
    'Arabic_2': (0x1000662, u'\u0662'),
    'Arabic_3': (0x1000663, u'\u0663'),
    'Arabic_4': (0x1000664, u'\u0664'),
    'Arabic_5': (0x1000665, u'\u0665'),
    'Arabic_6': (0x1000666, u'\u0666'),
    'Arabic_7': (0x1000667, u'\u0667'),
    'Arabic_8': (0x1000668, u'\u0668'),
    'Arabic_9': (0x1000669, u'\u0669'),
    'Arabic_ain': (0x05d9, u'\u0639'),
    'Arabic_alef': (0x05c7, u'\u0627'),
    'Arabic_alefmaksura': (0x05e9, u'\u0649'),
    'Arabic_beh': (0x05c8, u'\u0628'),
    'Arabic_comma': (0x05ac, u'\u060C'),
    'Arabic_dad': (0x05d6, u'\u0636'),
    'Arabic_dal': (0x05cf, u'\u062F'),
    'Arabic_damma': (0x05ef, u'\u064F'),
    'Arabic_dammatan': (0x05ec, u'\u064C'),
    'Arabic_ddal': (0x1000688, u'\u0688'),
    'Arabic_farsi_yeh': (0x10006cc, u'\u06CC'),
    'Arabic_fatha': (0x05ee, u'\u064E'),
    'Arabic_fathatan': (0x05eb, u'\u064B'),
    'Arabic_feh': (0x05e1, u'\u0641'),
    'Arabic_fullstop': (0x10006d4, u'\u06D4'),
    'Arabic_gaf': (0x10006af, u'\u06AF'),
    'Arabic_ghain': (0x05da, u'\u063A'),
    'Arabic_ha': (0x05e7, u'\u0647'),
    'Arabic_hah': (0x05cd, u'\u062D'),
    'Arabic_hamza': (0x05c1, u'\u0621'),
    'Arabic_hamza_above': (0x1000654, u'\u0654'),
    'Arabic_hamza_below': (0x1000655, u'\u0655'),
    'Arabic_hamzaonalef': (0x05c3, u'\u0623'),
    'Arabic_hamzaonwaw': (0x05c4, u'\u0624'),
    'Arabic_hamzaonyeh': (0x05c6, u'\u0626'),
    'Arabic_hamzaunderalef': (0x05c5, u'\u0625'),
    'Arabic_heh_doachashmee': (0x10006be, u'\u06BE'),
    'Arabic_heh_goal': (0x10006c1, u'\u06C1'),
    'Arabic_jeem': (0x05cc, u'\u062C'),
    'Arabic_jeh': (0x1000698, u'\u0698'),
    'Arabic_kaf': (0x05e3, u'\u0643'),
    'Arabic_kasra': (0x05f0, u'\u0650'),
    'Arabic_kasratan': (0x05ed, u'\u064D'),
    'Arabic_keheh': (0x10006a9, u'\u06A9'),
    'Arabic_khah': (0x05ce, u'\u062E'),
    'Arabic_lam': (0x05e4, u'\u0644'),
    'Arabic_madda_above': (0x1000653, u'\u0653'),
    'Arabic_maddaonalef': (0x05c2, u'\u0622'),
    'Arabic_meem': (0x05e5, u'\u0645'),
    'Arabic_noon': (0x05e6, u'\u0646'),
    'Arabic_noon_ghunna': (0x10006ba, u'\u06BA'),
    'Arabic_peh': (0x100067e, u'\u067E'),
    'Arabic_percent': (0x100066a, u'\u066A'),
    'Arabic_qaf': (0x05e2, u'\u0642'),
    'Arabic_question_mark': (0x05bf, u'\u061F'),
    'Arabic_ra': (0x05d1, u'\u0631'),
    'Arabic_rreh': (0x1000691, u'\u0691'),
    'Arabic_sad': (0x05d5, u'\u0635'),
    'Arabic_seen': (0x05d3, u'\u0633'),
    'Arabic_semicolon': (0x05bb, u'\u061B'),
    'Arabic_shadda': (0x05f1, u'\u0651'),
    'Arabic_sheen': (0x05d4, u'\u0634'),
    'Arabic_sukun': (0x05f2, u'\u0652'),
    'Arabic_superscript_alef': (0x1000670, u'\u0670'),
    'Arabic_tah': (0x05d7, u'\u0637'),
    'Arabic_tatweel': (0x05e0, u'\u0640'),
    'Arabic_tcheh': (0x1000686, u'\u0686'),
    'Arabic_teh': (0x05ca, u'\u062A'),
    'Arabic_tehmarbuta': (0x05c9, u'\u0629'),
    'Arabic_thal': (0x05d0, u'\u0630'),
    'Arabic_theh': (0x05cb, u'\u062B'),
    'Arabic_tteh': (0x1000679, u'\u0679'),
    'Arabic_veh': (0x10006a4, u'\u06A4'),
    'Arabic_waw': (0x05e8, u'\u0648'),
    'Arabic_yeh': (0x05ea, u'\u064A'),
    'Arabic_yeh_baree': (0x10006d2, u'\u06D2'),
    'Arabic_zah': (0x05d8, u'\u0638'),
    'Arabic_zain': (0x05d2, u'\u0632'),
    'Aring': (0x00c5, u'\u00C5'),
    'Armenian_AT': (0x1000538, u'\u0538'),
    'Armenian_AYB': (0x1000531, u'\u0531'),
    'Armenian_BEN': (0x1000532, u'\u0532'),
    'Armenian_CHA': (0x1000549, u'\u0549'),
    'Armenian_DA': (0x1000534, u'\u0534'),
    'Armenian_DZA': (0x1000541, u'\u0541'),
    'Armenian_E': (0x1000537, u'\u0537'),
    'Armenian_FE': (0x1000556, u'\u0556'),
    'Armenian_GHAT': (0x1000542, u'\u0542'),
    'Armenian_GIM': (0x1000533, u'\u0533'),
    'Armenian_HI': (0x1000545, u'\u0545'),
    'Armenian_HO': (0x1000540, u'\u0540'),
    'Armenian_INI': (0x100053b, u'\u053B'),
    'Armenian_JE': (0x100054b, u'\u054B'),
    'Armenian_KE': (0x1000554, u'\u0554'),
    'Armenian_KEN': (0x100053f, u'\u053F'),
    'Armenian_KHE': (0x100053d, u'\u053D'),
    'Armenian_LYUN': (0x100053c, u'\u053C'),
    'Armenian_MEN': (0x1000544, u'\u0544'),
    'Armenian_NU': (0x1000546, u'\u0546'),
    'Armenian_O': (0x1000555, u'\u0555'),
    'Armenian_PE': (0x100054a, u'\u054A'),
    'Armenian_PYUR': (0x1000553, u'\u0553'),
    'Armenian_RA': (0x100054c, u'\u054C'),
    'Armenian_RE': (0x1000550, u'\u0550'),
    'Armenian_SE': (0x100054d, u'\u054D'),
    'Armenian_SHA': (0x1000547, u'\u0547'),
    'Armenian_TCHE': (0x1000543, u'\u0543'),
    'Armenian_TO': (0x1000539, u'\u0539'),
    'Armenian_TSA': (0x100053e, u'\u053E'),
    'Armenian_TSO': (0x1000551, u'\u0551'),
    'Armenian_TYUN': (0x100054f, u'\u054F'),
    'Armenian_VEV': (0x100054e, u'\u054E'),
    'Armenian_VO': (0x1000548, u'\u0548'),
    'Armenian_VYUN': (0x1000552, u'\u0552'),
    'Armenian_YECH': (0x1000535, u'\u0535'),
    'Armenian_ZA': (0x1000536, u'\u0536'),
    'Armenian_ZHE': (0x100053a, u'\u053A'),
    'Armenian_accent': (0x100055b, u'\u055B'),
    'Armenian_amanak': (0x100055c, u'\u055C'),
    'Armenian_apostrophe': (0x100055a, u'\u055A'),
    'Armenian_at': (0x1000568, u'\u0568'),
    'Armenian_ayb': (0x1000561, u'\u0561'),
    'Armenian_ben': (0x1000562, u'\u0562'),
    'Armenian_but': (0x100055d, u'\u055D'),
    'Armenian_cha': (0x1000579, u'\u0579'),
    'Armenian_da': (0x1000564, u'\u0564'),
    'Armenian_dza': (0x1000571, u'\u0571'),
    'Armenian_e': (0x1000567, u'\u0567'),
    'Armenian_exclam': (0x100055c, u'\u055C'),
    'Armenian_fe': (0x1000586, u'\u0586'),
    'Armenian_full_stop': (0x1000589, u'\u0589'),
    'Armenian_ghat': (0x1000572, u'\u0572'),
    'Armenian_gim': (0x1000563, u'\u0563'),
    'Armenian_hi': (0x1000575, u'\u0575'),
    'Armenian_ho': (0x1000570, u'\u0570'),
    'Armenian_hyphen': (0x100058a, u'\u058A'),
    'Armenian_ini': (0x100056b, u'\u056B'),
    'Armenian_je': (0x100057b, u'\u057B'),
    'Armenian_ke': (0x1000584, u'\u0584'),
    'Armenian_ken': (0x100056f, u'\u056F'),
    'Armenian_khe': (0x100056d, u'\u056D'),
    'Armenian_ligature_ew': (0x1000587, u'\u0587'),
    'Armenian_lyun': (0x100056c, u'\u056C'),
    'Armenian_men': (0x1000574, u'\u0574'),
    'Armenian_nu': (0x1000576, u'\u0576'),
    'Armenian_o': (0x1000585, u'\u0585'),
    'Armenian_paruyk': (0x100055e, u'\u055E'),
    'Armenian_pe': (0x100057a, u'\u057A'),
    'Armenian_pyur': (0x1000583, u'\u0583'),
    'Armenian_question': (0x100055e, u'\u055E'),
    'Armenian_ra': (0x100057c, u'\u057C'),
    'Armenian_re': (0x1000580, u'\u0580'),
    'Armenian_se': (0x100057d, u'\u057D'),
    'Armenian_separation_mark': (0x100055d, u'\u055D'),
    'Armenian_sha': (0x1000577, u'\u0577'),
    'Armenian_shesht': (0x100055b, u'\u055B'),
    'Armenian_tche': (0x1000573, u'\u0573'),
    'Armenian_to': (0x1000569, u'\u0569'),
    'Armenian_tsa': (0x100056e, u'\u056E'),
    'Armenian_tso': (0x1000581, u'\u0581'),
    'Armenian_tyun': (0x100057f, u'\u057F'),
    'Armenian_verjaket': (0x1000589, u'\u0589'),
    'Armenian_vev': (0x100057e, u'\u057E'),
    'Armenian_vo': (0x1000578, u'\u0578'),
    'Armenian_vyun': (0x1000582, u'\u0582'),
    'Armenian_yech': (0x1000565, u'\u0565'),
    'Armenian_yentamna': (0x100058a, u'\u058A'),
    'Armenian_za': (0x1000566, u'\u0566'),
    'Armenian_zhe': (0x100056a, u'\u056A'),
    'Atilde': (0x00c3, u'\u00C3'),
    'B': (0x0042, u'\u0042'),
    'Babovedot': (0x1001e02, u'\u1E02'),
    'Byelorussian_SHORTU': (0x06be, u'\u040E'),
    'Byelorussian_shortu': (0x06ae, u'\u045E'),
    'C': (0x0043, u'\u0043'),
    'Cabovedot': (0x02c5, u'\u010A'),
    'Cacute': (0x01c6, u'\u0106'),
    'Ccaron': (0x01c8, u'\u010C'),
    'Ccedilla': (0x00c7, u'\u00C7'),
    'Ccircumflex': (0x02c6, u'\u0108'),
    'ColonSign': (0x10020a1, u'\u20A1'),
    'CruzeiroSign': (0x10020a2, u'\u20A2'),
    'Cyrillic_A': (0x06e1, u'\u0410'),
    'Cyrillic_BE': (0x06e2, u'\u0411'),
    'Cyrillic_CHE': (0x06fe, u'\u0427'),
    'Cyrillic_CHE_descender': (0x10004b6, u'\u04B6'),
    'Cyrillic_CHE_vertstroke': (0x10004b8, u'\u04B8'),
    'Cyrillic_DE': (0x06e4, u'\u0414'),
    'Cyrillic_DZHE': (0x06bf, u'\u040F'),
    'Cyrillic_E': (0x06fc, u'\u042D'),
    'Cyrillic_EF': (0x06e6, u'\u0424'),
    'Cyrillic_EL': (0x06ec, u'\u041B'),
    'Cyrillic_EM': (0x06ed, u'\u041C'),
    'Cyrillic_EN': (0x06ee, u'\u041D'),
    'Cyrillic_EN_descender': (0x10004a2, u'\u04A2'),
    'Cyrillic_ER': (0x06f2, u'\u0420'),
    'Cyrillic_ES': (0x06f3, u'\u0421'),
    'Cyrillic_GHE': (0x06e7, u'\u0413'),
    'Cyrillic_GHE_bar': (0x1000492, u'\u0492'),
    'Cyrillic_HA': (0x06e8, u'\u0425'),
    'Cyrillic_HARDSIGN': (0x06ff, u'\u042A'),
    'Cyrillic_HA_descender': (0x10004b2, u'\u04B2'),
    'Cyrillic_I': (0x06e9, u'\u0418'),
    'Cyrillic_IE': (0x06e5, u'\u0415'),
    'Cyrillic_IO': (0x06b3, u'\u0401'),
    'Cyrillic_I_macron': (0x10004e2, u'\u04E2'),
    'Cyrillic_JE': (0x06b8, u'\u0408'),
    'Cyrillic_KA': (0x06eb, u'\u041A'),
    'Cyrillic_KA_descender': (0x100049a, u'\u049A'),
    'Cyrillic_KA_vertstroke': (0x100049c, u'\u049C'),
    'Cyrillic_LJE': (0x06b9, u'\u0409'),
    'Cyrillic_NJE': (0x06ba, u'\u040A'),
    'Cyrillic_O': (0x06ef, u'\u041E'),
    'Cyrillic_O_bar': (0x10004e8, u'\u04E8'),
    'Cyrillic_PE': (0x06f0, u'\u041F'),
    'Cyrillic_SCHWA': (0x10004d8, u'\u04D8'),
    'Cyrillic_SHA': (0x06fb, u'\u0428'),
    'Cyrillic_SHCHA': (0x06fd, u'\u0429'),
    'Cyrillic_SHHA': (0x10004ba, u'\u04BA'),
    'Cyrillic_SHORTI': (0x06ea, u'\u0419'),
    'Cyrillic_SOFTSIGN': (0x06f8, u'\u042C'),
    'Cyrillic_TE': (0x06f4, u'\u0422'),
    'Cyrillic_TSE': (0x06e3, u'\u0426'),
    'Cyrillic_U': (0x06f5, u'\u0423'),
    'Cyrillic_U_macron': (0x10004ee, u'\u04EE'),
    'Cyrillic_U_straight': (0x10004ae, u'\u04AE'),
    'Cyrillic_U_straight_bar': (0x10004b0, u'\u04B0'),
    'Cyrillic_VE': (0x06f7, u'\u0412'),
    'Cyrillic_YA': (0x06f1, u'\u042F'),
    'Cyrillic_YERU': (0x06f9, u'\u042B'),
    'Cyrillic_YU': (0x06e0, u'\u042E'),
    'Cyrillic_ZE': (0x06fa, u'\u0417'),
    'Cyrillic_ZHE': (0x06f6, u'\u0416'),
    'Cyrillic_ZHE_descender': (0x1000496, u'\u0496'),
    'Cyrillic_a': (0x06c1, u'\u0430'),
    'Cyrillic_be': (0x06c2, u'\u0431'),
    'Cyrillic_che': (0x06de, u'\u0447'),
    'Cyrillic_che_descender': (0x10004b7, u'\u04B7'),
    'Cyrillic_che_vertstroke': (0x10004b9, u'\u04B9'),
    'Cyrillic_de': (0x06c4, u'\u0434'),
    'Cyrillic_dzhe': (0x06af, u'\u045F'),
    'Cyrillic_e': (0x06dc, u'\u044D'),
    'Cyrillic_ef': (0x06c6, u'\u0444'),
    'Cyrillic_el': (0x06cc, u'\u043B'),
    'Cyrillic_em': (0x06cd, u'\u043C'),
    'Cyrillic_en': (0x06ce, u'\u043D'),
    'Cyrillic_en_descender': (0x10004a3, u'\u04A3'),
    'Cyrillic_er': (0x06d2, u'\u0440'),
    'Cyrillic_es': (0x06d3, u'\u0441'),
    'Cyrillic_ghe': (0x06c7, u'\u0433'),
    'Cyrillic_ghe_bar': (0x1000493, u'\u0493'),
    'Cyrillic_ha': (0x06c8, u'\u0445'),
    'Cyrillic_ha_descender': (0x10004b3, u'\u04B3'),
    'Cyrillic_hardsign': (0x06df, u'\u044A'),
    'Cyrillic_i': (0x06c9, u'\u0438'),
    'Cyrillic_i_macron': (0x10004e3, u'\u04E3'),
    'Cyrillic_ie': (0x06c5, u'\u0435'),
    'Cyrillic_io': (0x06a3, u'\u0451'),
    'Cyrillic_je': (0x06a8, u'\u0458'),
    'Cyrillic_ka': (0x06cb, u'\u043A'),
    'Cyrillic_ka_descender': (0x100049b, u'\u049B'),
    'Cyrillic_ka_vertstroke': (0x100049d, u'\u049D'),
    'Cyrillic_lje': (0x06a9, u'\u0459'),
    'Cyrillic_nje': (0x06aa, u'\u045A'),
    'Cyrillic_o': (0x06cf, u'\u043E'),
    'Cyrillic_o_bar': (0x10004e9, u'\u04E9'),
    'Cyrillic_pe': (0x06d0, u'\u043F'),
    'Cyrillic_schwa': (0x10004d9, u'\u04D9'),
    'Cyrillic_sha': (0x06db, u'\u0448'),
    'Cyrillic_shcha': (0x06dd, u'\u0449'),
    'Cyrillic_shha': (0x10004bb, u'\u04BB'),
    'Cyrillic_shorti': (0x06ca, u'\u0439'),
    'Cyrillic_softsign': (0x06d8, u'\u044C'),
    'Cyrillic_te': (0x06d4, u'\u0442'),
    'Cyrillic_tse': (0x06c3, u'\u0446'),
    'Cyrillic_u': (0x06d5, u'\u0443'),
    'Cyrillic_u_macron': (0x10004ef, u'\u04EF'),
    'Cyrillic_u_straight': (0x10004af, u'\u04AF'),
    'Cyrillic_u_straight_bar': (0x10004b1, u'\u04B1'),
    'Cyrillic_ve': (0x06d7, u'\u0432'),
    'Cyrillic_ya': (0x06d1, u'\u044F'),
    'Cyrillic_yeru': (0x06d9, u'\u044B'),
    'Cyrillic_yu': (0x06c0, u'\u044E'),
    'Cyrillic_ze': (0x06da, u'\u0437'),
    'Cyrillic_zhe': (0x06d6, u'\u0436'),
    'Cyrillic_zhe_descender': (0x1000497, u'\u0497'),
    'D': (0x0044, u'\u0044'),
    'Dabovedot': (0x1001e0a, u'\u1E0A'),
    'Dcaron': (0x01cf, u'\u010E'),
    'DongSign': (0x10020ab, u'\u20AB'),
    'Dstroke': (0x01d0, u'\u0110'),
    'E': (0x0045, u'\u0045'),
    'ENG': (0x03bd, u'\u014A'),
    'ETH': (0x00d0, u'\u00D0'),
    'EZH': (0x10001b7, u'\u01B7'),
    'Eabovedot': (0x03cc, u'\u0116'),
    'Eacute': (0x00c9, u'\u00C9'),
    'Ebelowdot': (0x1001eb8, u'\u1EB8'),
    'Ecaron': (0x01cc, u'\u011A'),
    'Ecircumflex': (0x00ca, u'\u00CA'),
    'Ecircumflexacute': (0x1001ebe, u'\u1EBE'),
    'Ecircumflexbelowdot': (0x1001ec6, u'\u1EC6'),
    'Ecircumflexgrave': (0x1001ec0, u'\u1EC0'),
    'Ecircumflexhook': (0x1001ec2, u'\u1EC2'),
    'Ecircumflextilde': (0x1001ec4, u'\u1EC4'),
    'EcuSign': (0x10020a0, u'\u20A0'),
    'Ediaeresis': (0x00cb, u'\u00CB'),
    'Egrave': (0x00c8, u'\u00C8'),
    'Ehook': (0x1001eba, u'\u1EBA'),
    'Emacron': (0x03aa, u'\u0112'),
    'Eogonek': (0x01ca, u'\u0118'),
    'Etilde': (0x1001ebc, u'\u1EBC'),
    'EuroSign': (0x20ac, u'\u20AC'),
    'F': (0x0046, u'\u0046'),
    'FFrancSign': (0x10020a3, u'\u20A3'),
    'Fabovedot': (0x1001e1e, u'\u1E1E'),
    'Farsi_0': (0x10006f0, u'\u06F0'),
    'Farsi_1': (0x10006f1, u'\u06F1'),
    'Farsi_2': (0x10006f2, u'\u06F2'),
    'Farsi_3': (0x10006f3, u'\u06F3'),
    'Farsi_4': (0x10006f4, u'\u06F4'),
    'Farsi_5': (0x10006f5, u'\u06F5'),
    'Farsi_6': (0x10006f6, u'\u06F6'),
    'Farsi_7': (0x10006f7, u'\u06F7'),
    'Farsi_8': (0x10006f8, u'\u06F8'),
    'Farsi_9': (0x10006f9, u'\u06F9'),
    'Farsi_yeh': (0x10006cc, u'\u06CC'),
    'G': (0x0047, u'\u0047'),
    'Gabovedot': (0x02d5, u'\u0120'),
    'Gbreve': (0x02ab, u'\u011E'),
    'Gcaron': (0x10001e6, u'\u01E6'),
    'Gcedilla': (0x03ab, u'\u0122'),
    'Gcircumflex': (0x02d8, u'\u011C'),
    'Georgian_an': (0x10010d0, u'\u10D0'),
    'Georgian_ban': (0x10010d1, u'\u10D1'),
    'Georgian_can': (0x10010ea, u'\u10EA'),
    'Georgian_char': (0x10010ed, u'\u10ED'),
    'Georgian_chin': (0x10010e9, u'\u10E9'),
    'Georgian_cil': (0x10010ec, u'\u10EC'),
    'Georgian_don': (0x10010d3, u'\u10D3'),
    'Georgian_en': (0x10010d4, u'\u10D4'),
    'Georgian_fi': (0x10010f6, u'\u10F6'),
    'Georgian_gan': (0x10010d2, u'\u10D2'),
    'Georgian_ghan': (0x10010e6, u'\u10E6'),
    'Georgian_hae': (0x10010f0, u'\u10F0'),
    'Georgian_har': (0x10010f4, u'\u10F4'),
    'Georgian_he': (0x10010f1, u'\u10F1'),
    'Georgian_hie': (0x10010f2, u'\u10F2'),
    'Georgian_hoe': (0x10010f5, u'\u10F5'),
    'Georgian_in': (0x10010d8, u'\u10D8'),
    'Georgian_jhan': (0x10010ef, u'\u10EF'),
    'Georgian_jil': (0x10010eb, u'\u10EB'),
    'Georgian_kan': (0x10010d9, u'\u10D9'),
    'Georgian_khar': (0x10010e5, u'\u10E5'),
    'Georgian_las': (0x10010da, u'\u10DA'),
    'Georgian_man': (0x10010db, u'\u10DB'),
    'Georgian_nar': (0x10010dc, u'\u10DC'),
    'Georgian_on': (0x10010dd, u'\u10DD'),
    'Georgian_par': (0x10010de, u'\u10DE'),
    'Georgian_phar': (0x10010e4, u'\u10E4'),
    'Georgian_qar': (0x10010e7, u'\u10E7'),
    'Georgian_rae': (0x10010e0, u'\u10E0'),
    'Georgian_san': (0x10010e1, u'\u10E1'),
    'Georgian_shin': (0x10010e8, u'\u10E8'),
    'Georgian_tan': (0x10010d7, u'\u10D7'),
    'Georgian_tar': (0x10010e2, u'\u10E2'),
    'Georgian_un': (0x10010e3, u'\u10E3'),
    'Georgian_vin': (0x10010d5, u'\u10D5'),
    'Georgian_we': (0x10010f3, u'\u10F3'),
    'Georgian_xan': (0x10010ee, u'\u10EE'),
    'Georgian_zen': (0x10010d6, u'\u10D6'),
    'Georgian_zhar': (0x10010df, u'\u10DF'),
    'Greek_ALPHA': (0x07c1, u'\u0391'),
    'Greek_ALPHAaccent': (0x07a1, u'\u0386'),
    'Greek_BETA': (0x07c2, u'\u0392'),
    'Greek_CHI': (0x07d7, u'\u03A7'),
    'Greek_DELTA': (0x07c4, u'\u0394'),
    'Greek_EPSILON': (0x07c5, u'\u0395'),
    'Greek_EPSILONaccent': (0x07a2, u'\u0388'),
    'Greek_ETA': (0x07c7, u'\u0397'),
    'Greek_ETAaccent': (0x07a3, u'\u0389'),
    'Greek_GAMMA': (0x07c3, u'\u0393'),
    'Greek_IOTA': (0x07c9, u'\u0399'),
    'Greek_IOTAaccent': (0x07a4, u'\u038A'),
    'Greek_IOTAdieresis': (0x07a5, u'\u03AA'),
    'Greek_KAPPA': (0x07ca, u'\u039A'),
    'Greek_LAMBDA': (0x07cb, u'\u039B'),
    'Greek_LAMDA': (0x07cb, u'\u039B'),
    'Greek_MU': (0x07cc, u'\u039C'),
    'Greek_NU': (0x07cd, u'\u039D'),
    'Greek_OMEGA': (0x07d9, u'\u03A9'),
    'Greek_OMEGAaccent': (0x07ab, u'\u038F'),
    'Greek_OMICRON': (0x07cf, u'\u039F'),
    'Greek_OMICRONaccent': (0x07a7, u'\u038C'),
    'Greek_PHI': (0x07d6, u'\u03A6'),
    'Greek_PI': (0x07d0, u'\u03A0'),
    'Greek_PSI': (0x07d8, u'\u03A8'),
    'Greek_RHO': (0x07d1, u'\u03A1'),
    'Greek_SIGMA': (0x07d2, u'\u03A3'),
    'Greek_TAU': (0x07d4, u'\u03A4'),
    'Greek_THETA': (0x07c8, u'\u0398'),
    'Greek_UPSILON': (0x07d5, u'\u03A5'),
    'Greek_UPSILONaccent': (0x07a8, u'\u038E'),
    'Greek_UPSILONdieresis': (0x07a9, u'\u03AB'),
    'Greek_XI': (0x07ce, u'\u039E'),
    'Greek_ZETA': (0x07c6, u'\u0396'),
    'Greek_accentdieresis': (0x07ae, u'\u0385'),
    'Greek_alpha': (0x07e1, u'\u03B1'),
    'Greek_alphaaccent': (0x07b1, u'\u03AC'),
    'Greek_beta': (0x07e2, u'\u03B2'),
    'Greek_chi': (0x07f7, u'\u03C7'),
    'Greek_delta': (0x07e4, u'\u03B4'),
    'Greek_epsilon': (0x07e5, u'\u03B5'),
    'Greek_epsilonaccent': (0x07b2, u'\u03AD'),
    'Greek_eta': (0x07e7, u'\u03B7'),
    'Greek_etaaccent': (0x07b3, u'\u03AE'),
    'Greek_finalsmallsigma': (0x07f3, u'\u03C2'),
    'Greek_gamma': (0x07e3, u'\u03B3'),
    'Greek_horizbar': (0x07af, u'\u2015'),
    'Greek_iota': (0x07e9, u'\u03B9'),
    'Greek_iotaaccent': (0x07b4, u'\u03AF'),
    'Greek_iotaaccentdieresis': (0x07b6, u'\u0390'),
    'Greek_iotadieresis': (0x07b5, u'\u03CA'),
    'Greek_kappa': (0x07ea, u'\u03BA'),
    'Greek_lambda': (0x07eb, u'\u03BB'),
    'Greek_lamda': (0x07eb, u'\u03BB'),
    'Greek_mu': (0x07ec, u'\u03BC'),
    'Greek_nu': (0x07ed, u'\u03BD'),
    'Greek_omega': (0x07f9, u'\u03C9'),
    'Greek_omegaaccent': (0x07bb, u'\u03CE'),
    'Greek_omicron': (0x07ef, u'\u03BF'),
    'Greek_omicronaccent': (0x07b7, u'\u03CC'),
    'Greek_phi': (0x07f6, u'\u03C6'),
    'Greek_pi': (0x07f0, u'\u03C0'),
    'Greek_psi': (0x07f8, u'\u03C8'),
    'Greek_rho': (0x07f1, u'\u03C1'),
    'Greek_sigma': (0x07f2, u'\u03C3'),
    'Greek_tau': (0x07f4, u'\u03C4'),
    'Greek_theta': (0x07e8, u'\u03B8'),
    'Greek_upsilon': (0x07f5, u'\u03C5'),
    'Greek_upsilonaccent': (0x07b8, u'\u03CD'),
    'Greek_upsilonaccentdieresis': (0x07ba, u'\u03B0'),
    'Greek_upsilondieresis': (0x07b9, u'\u03CB'),
    'Greek_xi': (0x07ee, u'\u03BE'),
    'Greek_zeta': (0x07e6, u'\u03B6'),
    'H': (0x0048, u'\u0048'),
    'Hcircumflex': (0x02a6, u'\u0124'),
    'Hstroke': (0x02a1, u'\u0126'),
    'I': (0x0049, u'\u0049'),
    'Iabovedot': (0x02a9, u'\u0130'),
    'Iacute': (0x00cd, u'\u00CD'),
    'Ibelowdot': (0x1001eca, u'\u1ECA'),
    'Ibreve': (0x100012c, u'\u012C'),
    'Icircumflex': (0x00ce, u'\u00CE'),
    'Idiaeresis': (0x00cf, u'\u00CF'),
    'Igrave': (0x00cc, u'\u00CC'),
    'Ihook': (0x1001ec8, u'\u1EC8'),
    'Imacron': (0x03cf, u'\u012A'),
    'Iogonek': (0x03c7, u'\u012E'),
    'Itilde': (0x03a5, u'\u0128'),
    'J': (0x004a, u'\u004A'),
    'Jcircumflex': (0x02ac, u'\u0134'),
    'K': (0x004b, u'\u004B'),
    'KP_0': (0xffb0, None),
    'KP_1': (0xffb1, None),
    'KP_2': (0xffb2, None),
    'KP_3': (0xffb3, None),
    'KP_4': (0xffb4, None),
    'KP_5': (0xffb5, None),
    'KP_6': (0xffb6, None),
    'KP_7': (0xffb7, None),
    'KP_8': (0xffb8, None),
    'KP_9': (0xffb9, None),
    'KP_Add': (0xffab, None),
    'KP_Begin': (0xff9d, None),
    'KP_Decimal': (0xffae, None),
    'KP_Delete': (0xff9f, None),
    'KP_Divide': (0xffaf, None),
    'KP_Down': (0xff99, None),
    'KP_End': (0xff9c, None),
    'KP_Enter': (0xff8d, None),
    'KP_Equal': (0xffbd, None),
    'KP_F1': (0xff91, None),
    'KP_F2': (0xff92, None),
    'KP_F3': (0xff93, None),
    'KP_F4': (0xff94, None),
    'KP_Home': (0xff95, None),
    'KP_Insert': (0xff9e, None),
    'KP_Left': (0xff96, None),
    'KP_Multiply': (0xffaa, None),
    'KP_Next': (0xff9b, None),
    'KP_Page_Down': (0xff9b, None),
    'KP_Page_Up': (0xff9a, None),
    'KP_Prior': (0xff9a, None),
    'KP_Right': (0xff98, None),
    'KP_Separator': (0xffac, None),
    'KP_Space': (0xff80, None),
    'KP_Subtract': (0xffad, None),
    'KP_Tab': (0xff89, None),
    'KP_Up': (0xff97, None),
    'Kcedilla': (0x03d3, u'\u0136'),
    'L': (0x004c, u'\u004C'),
    'Lacute': (0x01c5, u'\u0139'),
    'Lbelowdot': (0x1001e36, u'\u1E36'),
    'Lcaron': (0x01a5, u'\u013D'),
    'Lcedilla': (0x03a6, u'\u013B'),
    'LiraSign': (0x10020a4, u'\u20A4'),
    'Lstroke': (0x01a3, u'\u0141'),
    'M': (0x004d, u'\u004D'),
    'Mabovedot': (0x1001e40, u'\u1E40'),
    'Macedonia_DSE': (0x06b5, u'\u0405'),
    'Macedonia_GJE': (0x06b2, u'\u0403'),
    'Macedonia_KJE': (0x06bc, u'\u040C'),
    'Macedonia_dse': (0x06a5, u'\u0455'),
    'Macedonia_gje': (0x06a2, u'\u0453'),
    'Macedonia_kje': (0x06ac, u'\u045C'),
    'MillSign': (0x10020a5, u'\u20A5'),
    'N': (0x004e, u'\u004E'),
    'Nacute': (0x01d1, u'\u0143'),
    'NairaSign': (0x10020a6, u'\u20A6'),
    'Ncaron': (0x01d2, u'\u0147'),
    'Ncedilla': (0x03d1, u'\u0145'),
    'NewSheqelSign': (0x10020aa, u'\u20AA'),
    'Ntilde': (0x00d1, u'\u00D1'),
    'O': (0x004f, u'\u004F'),
    'OE': (0x13bc, u'\u0152'),
    'Oacute': (0x00d3, u'\u00D3'),
    'Obarred': (0x100019f, u'\u019F'),
    'Obelowdot': (0x1001ecc, u'\u1ECC'),
    'Ocaron': (0x10001d1, u'\u01D2'),
    'Ocircumflex': (0x00d4, u'\u00D4'),
    'Ocircumflexacute': (0x1001ed0, u'\u1ED0'),
    'Ocircumflexbelowdot': (0x1001ed8, u'\u1ED8'),
    'Ocircumflexgrave': (0x1001ed2, u'\u1ED2'),
    'Ocircumflexhook': (0x1001ed4, u'\u1ED4'),
    'Ocircumflextilde': (0x1001ed6, u'\u1ED6'),
    'Odiaeresis': (0x00d6, u'\u00D6'),
    'Odoubleacute': (0x01d5, u'\u0150'),
    'Ograve': (0x00d2, u'\u00D2'),
    'Ohook': (0x1001ece, u'\u1ECE'),
    'Ohorn': (0x10001a0, u'\u01A0'),
    'Ohornacute': (0x1001eda, u'\u1EDA'),
    'Ohornbelowdot': (0x1001ee2, u'\u1EE2'),
    'Ohorngrave': (0x1001edc, u'\u1EDC'),
    'Ohornhook': (0x1001ede, u'\u1EDE'),
    'Ohorntilde': (0x1001ee0, u'\u1EE0'),
    'Omacron': (0x03d2, u'\u014C'),
    'Ooblique': (0x00d8, u'\u00D8'),
    'Oslash': (0x00d8, u'\u00D8'),
    'Otilde': (0x00d5, u'\u00D5'),
    'P': (0x0050, u'\u0050'),
    'Pabovedot': (0x1001e56, u'\u1E56'),
    'PesetaSign': (0x10020a7, u'\u20A7'),
    'Q': (0x0051, u'\u0051'),
    'R': (0x0052, u'\u0052'),
    'Racute': (0x01c0, u'\u0154'),
    'Rcaron': (0x01d8, u'\u0158'),
    'Rcedilla': (0x03a3, u'\u0156'),
    'RupeeSign': (0x10020a8, u'\u20A8'),
    'S': (0x0053, u'\u0053'),
    'SCHWA': (0x100018f, u'\u018F'),
    'Sabovedot': (0x1001e60, u'\u1E60'),
    'Sacute': (0x01a6, u'\u015A'),
    'Scaron': (0x01a9, u'\u0160'),
    'Scedilla': (0x01aa, u'\u015E'),
    'Scircumflex': (0x02de, u'\u015C'),
    'Serbian_DJE': (0x06b1, u'\u0402'),
    'Serbian_TSHE': (0x06bb, u'\u040B'),
    'Serbian_dje': (0x06a1, u'\u0452'),
    'Serbian_tshe': (0x06ab, u'\u045B'),
    'Sinh_a': (0x1000d85, u'\u0D85'),
    'Sinh_aa': (0x1000d86, u'\u0D86'),
    'Sinh_aa2': (0x1000dcf, u'\u0DCF'),
    'Sinh_ae': (0x1000d87, u'\u0D87'),
    'Sinh_ae2': (0x1000dd0, u'\u0DD0'),
    'Sinh_aee': (0x1000d88, u'\u0D88'),
    'Sinh_aee2': (0x1000dd1, u'\u0DD1'),
    'Sinh_ai': (0x1000d93, u'\u0D93'),
    'Sinh_ai2': (0x1000ddb, u'\u0DDB'),
    'Sinh_al': (0x1000dca, u'\u0DCA'),
    'Sinh_au': (0x1000d96, u'\u0D96'),
    'Sinh_au2': (0x1000dde, u'\u0DDE'),
    'Sinh_ba': (0x1000db6, u'\u0DB6'),
    'Sinh_bha': (0x1000db7, u'\u0DB7'),
    'Sinh_ca': (0x1000da0, u'\u0DA0'),
    'Sinh_cha': (0x1000da1, u'\u0DA1'),
    'Sinh_dda': (0x1000da9, u'\u0DA9'),
    'Sinh_ddha': (0x1000daa, u'\u0DAA'),
    'Sinh_dha': (0x1000daf, u'\u0DAF'),
    'Sinh_dhha': (0x1000db0, u'\u0DB0'),
    'Sinh_e': (0x1000d91, u'\u0D91'),
    'Sinh_e2': (0x1000dd9, u'\u0DD9'),
    'Sinh_ee': (0x1000d92, u'\u0D92'),
    'Sinh_ee2': (0x1000dda, u'\u0DDA'),
    'Sinh_fa': (0x1000dc6, u'\u0DC6'),
    'Sinh_ga': (0x1000d9c, u'\u0D9C'),
    'Sinh_gha': (0x1000d9d, u'\u0D9D'),
    'Sinh_h2': (0x1000d83, u'\u0D83'),
    'Sinh_ha': (0x1000dc4, u'\u0DC4'),
    'Sinh_i': (0x1000d89, u'\u0D89'),
    'Sinh_i2': (0x1000dd2, u'\u0DD2'),
    'Sinh_ii': (0x1000d8a, u'\u0D8A'),
    'Sinh_ii2': (0x1000dd3, u'\u0DD3'),
    'Sinh_ja': (0x1000da2, u'\u0DA2'),
    'Sinh_jha': (0x1000da3, u'\u0DA3'),
    'Sinh_jnya': (0x1000da5, u'\u0DA5'),
    'Sinh_ka': (0x1000d9a, u'\u0D9A'),
    'Sinh_kha': (0x1000d9b, u'\u0D9B'),
    'Sinh_kunddaliya': (0x1000df4, u'\u0DF4'),
    'Sinh_la': (0x1000dbd, u'\u0DBD'),
    'Sinh_lla': (0x1000dc5, u'\u0DC5'),
    'Sinh_lu': (0x1000d8f, u'\u0D8F'),
    'Sinh_lu2': (0x1000ddf, u'\u0DDF'),
    'Sinh_luu': (0x1000d90, u'\u0D90'),
    'Sinh_luu2': (0x1000df3, u'\u0DF3'),
    'Sinh_ma': (0x1000db8, u'\u0DB8'),
    'Sinh_mba': (0x1000db9, u'\u0DB9'),
    'Sinh_na': (0x1000db1, u'\u0DB1'),
    'Sinh_ndda': (0x1000dac, u'\u0DAC'),
    'Sinh_ndha': (0x1000db3, u'\u0DB3'),
    'Sinh_ng': (0x1000d82, u'\u0D82'),
    'Sinh_ng2': (0x1000d9e, u'\u0D9E'),
    'Sinh_nga': (0x1000d9f, u'\u0D9F'),
    'Sinh_nja': (0x1000da6, u'\u0DA6'),
    'Sinh_nna': (0x1000dab, u'\u0DAB'),
    'Sinh_nya': (0x1000da4, u'\u0DA4'),
    'Sinh_o': (0x1000d94, u'\u0D94'),
    'Sinh_o2': (0x1000ddc, u'\u0DDC'),
    'Sinh_oo': (0x1000d95, u'\u0D95'),
    'Sinh_oo2': (0x1000ddd, u'\u0DDD'),
    'Sinh_pa': (0x1000db4, u'\u0DB4'),
    'Sinh_pha': (0x1000db5, u'\u0DB5'),
    'Sinh_ra': (0x1000dbb, u'\u0DBB'),
    'Sinh_ri': (0x1000d8d, u'\u0D8D'),
    'Sinh_rii': (0x1000d8e, u'\u0D8E'),
    'Sinh_ru2': (0x1000dd8, u'\u0DD8'),
    'Sinh_ruu2': (0x1000df2, u'\u0DF2'),
    'Sinh_sa': (0x1000dc3, u'\u0DC3'),
    'Sinh_sha': (0x1000dc1, u'\u0DC1'),
    'Sinh_ssha': (0x1000dc2, u'\u0DC2'),
    'Sinh_tha': (0x1000dad, u'\u0DAD'),
    'Sinh_thha': (0x1000dae, u'\u0DAE'),
    'Sinh_tta': (0x1000da7, u'\u0DA7'),
    'Sinh_ttha': (0x1000da8, u'\u0DA8'),
    'Sinh_u': (0x1000d8b, u'\u0D8B'),
    'Sinh_u2': (0x1000dd4, u'\u0DD4'),
    'Sinh_uu': (0x1000d8c, u'\u0D8C'),
    'Sinh_uu2': (0x1000dd6, u'\u0DD6'),
    'Sinh_va': (0x1000dc0, u'\u0DC0'),
    'Sinh_ya': (0x1000dba, u'\u0DBA'),
    'T': (0x0054, u'\u0054'),
    'THORN': (0x00de, u'\u00DE'),
    'Tabovedot': (0x1001e6a, u'\u1E6A'),
    'Tcaron': (0x01ab, u'\u0164'),
    'Tcedilla': (0x01de, u'\u0162'),
    'Thai_baht': (0x0ddf, u'\u0E3F'),
    'Thai_bobaimai': (0x0dba, u'\u0E1A'),
    'Thai_chochan': (0x0da8, u'\u0E08'),
    'Thai_chochang': (0x0daa, u'\u0E0A'),
    'Thai_choching': (0x0da9, u'\u0E09'),
    'Thai_chochoe': (0x0dac, u'\u0E0C'),
    'Thai_dochada': (0x0dae, u'\u0E0E'),
    'Thai_dodek': (0x0db4, u'\u0E14'),
    'Thai_fofa': (0x0dbd, u'\u0E1D'),
    'Thai_fofan': (0x0dbf, u'\u0E1F'),
    'Thai_hohip': (0x0dcb, u'\u0E2B'),
    'Thai_honokhuk': (0x0dce, u'\u0E2E'),
    'Thai_khokhai': (0x0da2, u'\u0E02'),
    'Thai_khokhon': (0x0da5, u'\u0E05'),
    'Thai_khokhuat': (0x0da3, u'\u0E03'),
    'Thai_khokhwai': (0x0da4, u'\u0E04'),
    'Thai_khorakhang': (0x0da6, u'\u0E06'),
    'Thai_kokai': (0x0da1, u'\u0E01'),
    'Thai_lakkhangyao': (0x0de5, u'\u0E45'),
    'Thai_lekchet': (0x0df7, u'\u0E57'),
    'Thai_lekha': (0x0df5, u'\u0E55'),
    'Thai_lekhok': (0x0df6, u'\u0E56'),
    'Thai_lekkao': (0x0df9, u'\u0E59'),
    'Thai_leknung': (0x0df1, u'\u0E51'),
    'Thai_lekpaet': (0x0df8, u'\u0E58'),
    'Thai_leksam': (0x0df3, u'\u0E53'),
    'Thai_leksi': (0x0df4, u'\u0E54'),
    'Thai_leksong': (0x0df2, u'\u0E52'),
    'Thai_leksun': (0x0df0, u'\u0E50'),
    'Thai_lochula': (0x0dcc, u'\u0E2C'),
    'Thai_loling': (0x0dc5, u'\u0E25'),
    'Thai_lu': (0x0dc6, u'\u0E26'),
    'Thai_maichattawa': (0x0deb, u'\u0E4B'),
    'Thai_maiek': (0x0de8, u'\u0E48'),
    'Thai_maihanakat': (0x0dd1, u'\u0E31'),
    'Thai_maitaikhu': (0x0de7, u'\u0E47'),
    'Thai_maitho': (0x0de9, u'\u0E49'),
    'Thai_maitri': (0x0dea, u'\u0E4A'),
    'Thai_maiyamok': (0x0de6, u'\u0E46'),
    'Thai_moma': (0x0dc1, u'\u0E21'),
    'Thai_ngongu': (0x0da7, u'\u0E07'),
    'Thai_nikhahit': (0x0ded, u'\u0E4D'),
    'Thai_nonen': (0x0db3, u'\u0E13'),
    'Thai_nonu': (0x0db9, u'\u0E19'),
    'Thai_oang': (0x0dcd, u'\u0E2D'),
    'Thai_paiyannoi': (0x0dcf, u'\u0E2F'),
    'Thai_phinthu': (0x0dda, u'\u0E3A'),
    'Thai_phophan': (0x0dbe, u'\u0E1E'),
    'Thai_phophung': (0x0dbc, u'\u0E1C'),
    'Thai_phosamphao': (0x0dc0, u'\u0E20'),
    'Thai_popla': (0x0dbb, u'\u0E1B'),
    'Thai_rorua': (0x0dc3, u'\u0E23'),
    'Thai_ru': (0x0dc4, u'\u0E24'),
    'Thai_saraa': (0x0dd0, u'\u0E30'),
    'Thai_saraaa': (0x0dd2, u'\u0E32'),
    'Thai_saraae': (0x0de1, u'\u0E41'),
    'Thai_saraaimaimalai': (0x0de4, u'\u0E44'),
    'Thai_saraaimaimuan': (0x0de3, u'\u0E43'),
    'Thai_saraam': (0x0dd3, u'\u0E33'),
    'Thai_sarae': (0x0de0, u'\u0E40'),
    'Thai_sarai': (0x0dd4, u'\u0E34'),
    'Thai_saraii': (0x0dd5, u'\u0E35'),
    'Thai_sarao': (0x0de2, u'\u0E42'),
    'Thai_sarau': (0x0dd8, u'\u0E38'),
    'Thai_saraue': (0x0dd6, u'\u0E36'),
    'Thai_sarauee': (0x0dd7, u'\u0E37'),
    'Thai_sarauu': (0x0dd9, u'\u0E39'),
    'Thai_sorusi': (0x0dc9, u'\u0E29'),
    'Thai_sosala': (0x0dc8, u'\u0E28'),
    'Thai_soso': (0x0dab, u'\u0E0B'),
    'Thai_sosua': (0x0dca, u'\u0E2A'),
    'Thai_thanthakhat': (0x0dec, u'\u0E4C'),
    'Thai_thonangmontho': (0x0db1, u'\u0E11'),
    'Thai_thophuthao': (0x0db2, u'\u0E12'),
    'Thai_thothahan': (0x0db7, u'\u0E17'),
    'Thai_thothan': (0x0db0, u'\u0E10'),
    'Thai_thothong': (0x0db8, u'\u0E18'),
    'Thai_thothung': (0x0db6, u'\u0E16'),
    'Thai_topatak': (0x0daf, u'\u0E0F'),
    'Thai_totao': (0x0db5, u'\u0E15'),
    'Thai_wowaen': (0x0dc7, u'\u0E27'),
    'Thai_yoyak': (0x0dc2, u'\u0E22'),
    'Thai_yoying': (0x0dad, u'\u0E0D'),
    'Tslash': (0x03ac, u'\u0166'),
    'U': (0x0055, u'\u0055'),
    'Uacute': (0x00da, u'\u00DA'),
    'Ubelowdot': (0x1001ee4, u'\u1EE4'),
    'Ubreve': (0x02dd, u'\u016C'),
    'Ucircumflex': (0x00db, u'\u00DB'),
    'Udiaeresis': (0x00dc, u'\u00DC'),
    'Udoubleacute': (0x01db, u'\u0170'),
    'Ugrave': (0x00d9, u'\u00D9'),
    'Uhook': (0x1001ee6, u'\u1EE6'),
    'Uhorn': (0x10001af, u'\u01AF'),
    'Uhornacute': (0x1001ee8, u'\u1EE8'),
    'Uhornbelowdot': (0x1001ef0, u'\u1EF0'),
    'Uhorngrave': (0x1001eea, u'\u1EEA'),
    'Uhornhook': (0x1001eec, u'\u1EEC'),
    'Uhorntilde': (0x1001eee, u'\u1EEE'),
    'Ukrainian_GHE_WITH_UPTURN': (0x06bd, u'\u0490'),
    'Ukrainian_I': (0x06b6, u'\u0406'),
    'Ukrainian_IE': (0x06b4, u'\u0404'),
    'Ukrainian_YI': (0x06b7, u'\u0407'),
    'Ukrainian_ghe_with_upturn': (0x06ad, u'\u0491'),
    'Ukrainian_i': (0x06a6, u'\u0456'),
    'Ukrainian_ie': (0x06a4, u'\u0454'),
    'Ukrainian_yi': (0x06a7, u'\u0457'),
    'Umacron': (0x03de, u'\u016A'),
    'Uogonek': (0x03d9, u'\u0172'),
    'Uring': (0x01d9, u'\u016E'),
    'Utilde': (0x03dd, u'\u0168'),
    'V': (0x0056, u'\u0056'),
    'W': (0x0057, u'\u0057'),
    'Wacute': (0x1001e82, u'\u1E82'),
    'Wcircumflex': (0x1000174, u'\u0174'),
    'Wdiaeresis': (0x1001e84, u'\u1E84'),
    'Wgrave': (0x1001e80, u'\u1E80'),
    'WonSign': (0x10020a9, u'\u20A9'),
    'X': (0x0058, u'\u0058'),
    'Xabovedot': (0x1001e8a, u'\u1E8A'),
    'Y': (0x0059, u'\u0059'),
    'Yacute': (0x00dd, u'\u00DD'),
    'Ybelowdot': (0x1001ef4, u'\u1EF4'),
    'Ycircumflex': (0x1000176, u'\u0176'),
    'Ydiaeresis': (0x13be, u'\u0178'),
    'Ygrave': (0x1001ef2, u'\u1EF2'),
    'Yhook': (0x1001ef6, u'\u1EF6'),
    'Ytilde': (0x1001ef8, u'\u1EF8'),
    'Z': (0x005a, u'\u005A'),
    'Zabovedot': (0x01af, u'\u017B'),
    'Zacute': (0x01ac, u'\u0179'),
    'Zcaron': (0x01ae, u'\u017D'),
    'Zstroke': (0x10001b5, u'\u01B5'),
    'a': (0x0061, u'\u0061'),
    'aacute': (0x00e1, u'\u00E1'),
    'abelowdot': (0x1001ea1, u'\u1EA1'),
    'abovedot': (0x01ff, u'\u02D9'),
    'abreve': (0x01e3, u'\u0103'),
    'abreveacute': (0x1001eaf, u'\u1EAF'),
    'abrevebelowdot': (0x1001eb7, u'\u1EB7'),
    'abrevegrave': (0x1001eb1, u'\u1EB1'),
    'abrevehook': (0x1001eb3, u'\u1EB3'),
    'abrevetilde': (0x1001eb5, u'\u1EB5'),
    'acircumflex': (0x00e2, u'\u00E2'),
    'acircumflexacute': (0x1001ea5, u'\u1EA5'),
    'acircumflexbelowdot': (0x1001ead, u'\u1EAD'),
    'acircumflexgrave': (0x1001ea7, u'\u1EA7'),
    'acircumflexhook': (0x1001ea9, u'\u1EA9'),
    'acircumflextilde': (0x1001eab, u'\u1EAB'),
    'acute': (0x00b4, u'\u00B4'),
    'adiaeresis': (0x00e4, u'\u00E4'),
    'ae': (0x00e6, u'\u00E6'),
    'agrave': (0x00e0, u'\u00E0'),
    'ahook': (0x1001ea3, u'\u1EA3'),
    'amacron': (0x03e0, u'\u0101'),
    'ampersand': (0x0026, u'\u0026'),
    'aogonek': (0x01b1, u'\u0105'),
    'apostrophe': (0x0027, u'\u0027'),
    'approxeq': (0x1002248, u'\u2245'),
    'approximate': (0x08c8, u'\u223C'),
    'aring': (0x00e5, u'\u00E5'),
    'asciicircum': (0x005e, u'\u005E'),
    'asciitilde': (0x007e, u'\u007E'),
    'asterisk': (0x002a, u'\u002A'),
    'at': (0x0040, u'\u0040'),
    'atilde': (0x00e3, u'\u00E3'),
    'b': (0x0062, u'\u0062'),
    'babovedot': (0x1001e03, u'\u1E03'),
    'backslash': (0x005c, u'\u005C'),
    'ballotcross': (0x0af4, u'\u2717'),
    'bar': (0x007c, u'\u007C'),
    'because': (0x1002235, u'\u2235'),
    'botintegral': (0x08a5, u'\u2321'),
    'botleftparens': (0x08ac, u'\u239D'),
    'botleftsqbracket': (0x08a8, u'\u23A3'),
    'botrightparens': (0x08ae, u'\u23A0'),
    'botrightsqbracket': (0x08aa, u'\u23A6'),
    'bott': (0x09f6, u'\u2534'),
    'braceleft': (0x007b, u'\u007B'),
    'braceright': (0x007d, u'\u007D'),
    'bracketleft': (0x005b, u'\u005B'),
    'bracketright': (0x005d, u'\u005D'),
    'braille_blank': (0x1002800, u'\u2800'),
    'braille_dots_1': (0x1002801, u'\u2801'),
    'braille_dots_12': (0x1002803, u'\u2803'),
    'braille_dots_123': (0x1002807, u'\u2807'),
    'braille_dots_1234': (0x100280f, u'\u280f'),
    'braille_dots_12345': (0x100281f, u'\u281f'),
    'braille_dots_123456': (0x100283f, u'\u283f'),
    'braille_dots_1234567': (0x100287f, u'\u287f'),
    'braille_dots_12345678': (0x10028ff, u'\u28ff'),
    'braille_dots_1234568': (0x10028bf, u'\u28bf'),
    'braille_dots_123457': (0x100285f, u'\u285f'),
    'braille_dots_1234578': (0x10028df, u'\u28df'),
    'braille_dots_123458': (0x100289f, u'\u289f'),
    'braille_dots_12346': (0x100282f, u'\u282f'),
    'braille_dots_123467': (0x100286f, u'\u286f'),
    'braille_dots_1234678': (0x10028ef, u'\u28ef'),
    'braille_dots_123468': (0x10028af, u'\u28af'),
    'braille_dots_12347': (0x100284f, u'\u284f'),
    'braille_dots_123478': (0x10028cf, u'\u28cf'),
    'braille_dots_12348': (0x100288f, u'\u288f'),
    'braille_dots_1235': (0x1002817, u'\u2817'),
    'braille_dots_12356': (0x1002837, u'\u2837'),
    'braille_dots_123567': (0x1002877, u'\u2877'),
    'braille_dots_1235678': (0x10028f7, u'\u28f7'),
    'braille_dots_123568': (0x10028b7, u'\u28b7'),
    'braille_dots_12357': (0x1002857, u'\u2857'),
    'braille_dots_123578': (0x10028d7, u'\u28d7'),
    'braille_dots_12358': (0x1002897, u'\u2897'),
    'braille_dots_1236': (0x1002827, u'\u2827'),
    'braille_dots_12367': (0x1002867, u'\u2867'),
    'braille_dots_123678': (0x10028e7, u'\u28e7'),
    'braille_dots_12368': (0x10028a7, u'\u28a7'),
    'braille_dots_1237': (0x1002847, u'\u2847'),
    'braille_dots_12378': (0x10028c7, u'\u28c7'),
    'braille_dots_1238': (0x1002887, u'\u2887'),
    'braille_dots_124': (0x100280b, u'\u280b'),
    'braille_dots_1245': (0x100281b, u'\u281b'),
    'braille_dots_12456': (0x100283b, u'\u283b'),
    'braille_dots_124567': (0x100287b, u'\u287b'),
    'braille_dots_1245678': (0x10028fb, u'\u28fb'),
    'braille_dots_124568': (0x10028bb, u'\u28bb'),
    'braille_dots_12457': (0x100285b, u'\u285b'),
    'braille_dots_124578': (0x10028db, u'\u28db'),
    'braille_dots_12458': (0x100289b, u'\u289b'),
    'braille_dots_1246': (0x100282b, u'\u282b'),
    'braille_dots_12467': (0x100286b, u'\u286b'),
    'braille_dots_124678': (0x10028eb, u'\u28eb'),
    'braille_dots_12468': (0x10028ab, u'\u28ab'),
    'braille_dots_1247': (0x100284b, u'\u284b'),
    'braille_dots_12478': (0x10028cb, u'\u28cb'),
    'braille_dots_1248': (0x100288b, u'\u288b'),
    'braille_dots_125': (0x1002813, u'\u2813'),
    'braille_dots_1256': (0x1002833, u'\u2833'),
    'braille_dots_12567': (0x1002873, u'\u2873'),
    'braille_dots_125678': (0x10028f3, u'\u28f3'),
    'braille_dots_12568': (0x10028b3, u'\u28b3'),
    'braille_dots_1257': (0x1002853, u'\u2853'),
    'braille_dots_12578': (0x10028d3, u'\u28d3'),
    'braille_dots_1258': (0x1002893, u'\u2893'),
    'braille_dots_126': (0x1002823, u'\u2823'),
    'braille_dots_1267': (0x1002863, u'\u2863'),
    'braille_dots_12678': (0x10028e3, u'\u28e3'),
    'braille_dots_1268': (0x10028a3, u'\u28a3'),
    'braille_dots_127': (0x1002843, u'\u2843'),
    'braille_dots_1278': (0x10028c3, u'\u28c3'),
    'braille_dots_128': (0x1002883, u'\u2883'),
    'braille_dots_13': (0x1002805, u'\u2805'),
    'braille_dots_134': (0x100280d, u'\u280d'),
    'braille_dots_1345': (0x100281d, u'\u281d'),
    'braille_dots_13456': (0x100283d, u'\u283d'),
    'braille_dots_134567': (0x100287d, u'\u287d'),
    'braille_dots_1345678': (0x10028fd, u'\u28fd'),
    'braille_dots_134568': (0x10028bd, u'\u28bd'),
    'braille_dots_13457': (0x100285d, u'\u285d'),
    'braille_dots_134578': (0x10028dd, u'\u28dd'),
    'braille_dots_13458': (0x100289d, u'\u289d'),
    'braille_dots_1346': (0x100282d, u'\u282d'),
    'braille_dots_13467': (0x100286d, u'\u286d'),
    'braille_dots_134678': (0x10028ed, u'\u28ed'),
    'braille_dots_13468': (0x10028ad, u'\u28ad'),
    'braille_dots_1347': (0x100284d, u'\u284d'),
    'braille_dots_13478': (0x10028cd, u'\u28cd'),
    'braille_dots_1348': (0x100288d, u'\u288d'),
    'braille_dots_135': (0x1002815, u'\u2815'),
    'braille_dots_1356': (0x1002835, u'\u2835'),
    'braille_dots_13567': (0x1002875, u'\u2875'),
    'braille_dots_135678': (0x10028f5, u'\u28f5'),
    'braille_dots_13568': (0x10028b5, u'\u28b5'),
    'braille_dots_1357': (0x1002855, u'\u2855'),
    'braille_dots_13578': (0x10028d5, u'\u28d5'),
    'braille_dots_1358': (0x1002895, u'\u2895'),
    'braille_dots_136': (0x1002825, u'\u2825'),
    'braille_dots_1367': (0x1002865, u'\u2865'),
    'braille_dots_13678': (0x10028e5, u'\u28e5'),
    'braille_dots_1368': (0x10028a5, u'\u28a5'),
    'braille_dots_137': (0x1002845, u'\u2845'),
    'braille_dots_1378': (0x10028c5, u'\u28c5'),
    'braille_dots_138': (0x1002885, u'\u2885'),
    'braille_dots_14': (0x1002809, u'\u2809'),
    'braille_dots_145': (0x1002819, u'\u2819'),
    'braille_dots_1456': (0x1002839, u'\u2839'),
    'braille_dots_14567': (0x1002879, u'\u2879'),
    'braille_dots_145678': (0x10028f9, u'\u28f9'),
    'braille_dots_14568': (0x10028b9, u'\u28b9'),
    'braille_dots_1457': (0x1002859, u'\u2859'),
    'braille_dots_14578': (0x10028d9, u'\u28d9'),
    'braille_dots_1458': (0x1002899, u'\u2899'),
    'braille_dots_146': (0x1002829, u'\u2829'),
    'braille_dots_1467': (0x1002869, u'\u2869'),
    'braille_dots_14678': (0x10028e9, u'\u28e9'),
    'braille_dots_1468': (0x10028a9, u'\u28a9'),
    'braille_dots_147': (0x1002849, u'\u2849'),
    'braille_dots_1478': (0x10028c9, u'\u28c9'),
    'braille_dots_148': (0x1002889, u'\u2889'),
    'braille_dots_15': (0x1002811, u'\u2811'),
    'braille_dots_156': (0x1002831, u'\u2831'),
    'braille_dots_1567': (0x1002871, u'\u2871'),
    'braille_dots_15678': (0x10028f1, u'\u28f1'),
    'braille_dots_1568': (0x10028b1, u'\u28b1'),
    'braille_dots_157': (0x1002851, u'\u2851'),
    'braille_dots_1578': (0x10028d1, u'\u28d1'),
    'braille_dots_158': (0x1002891, u'\u2891'),
    'braille_dots_16': (0x1002821, u'\u2821'),
    'braille_dots_167': (0x1002861, u'\u2861'),
    'braille_dots_1678': (0x10028e1, u'\u28e1'),
    'braille_dots_168': (0x10028a1, u'\u28a1'),
    'braille_dots_17': (0x1002841, u'\u2841'),
    'braille_dots_178': (0x10028c1, u'\u28c1'),
    'braille_dots_18': (0x1002881, u'\u2881'),
    'braille_dots_2': (0x1002802, u'\u2802'),
    'braille_dots_23': (0x1002806, u'\u2806'),
    'braille_dots_234': (0x100280e, u'\u280e'),
    'braille_dots_2345': (0x100281e, u'\u281e'),
    'braille_dots_23456': (0x100283e, u'\u283e'),
    'braille_dots_234567': (0x100287e, u'\u287e'),
    'braille_dots_2345678': (0x10028fe, u'\u28fe'),
    'braille_dots_234568': (0x10028be, u'\u28be'),
    'braille_dots_23457': (0x100285e, u'\u285e'),
    'braille_dots_234578': (0x10028de, u'\u28de'),
    'braille_dots_23458': (0x100289e, u'\u289e'),
    'braille_dots_2346': (0x100282e, u'\u282e'),
    'braille_dots_23467': (0x100286e, u'\u286e'),
    'braille_dots_234678': (0x10028ee, u'\u28ee'),
    'braille_dots_23468': (0x10028ae, u'\u28ae'),
    'braille_dots_2347': (0x100284e, u'\u284e'),
    'braille_dots_23478': (0x10028ce, u'\u28ce'),
    'braille_dots_2348': (0x100288e, u'\u288e'),
    'braille_dots_235': (0x1002816, u'\u2816'),
    'braille_dots_2356': (0x1002836, u'\u2836'),
    'braille_dots_23567': (0x1002876, u'\u2876'),
    'braille_dots_235678': (0x10028f6, u'\u28f6'),
    'braille_dots_23568': (0x10028b6, u'\u28b6'),
    'braille_dots_2357': (0x1002856, u'\u2856'),
    'braille_dots_23578': (0x10028d6, u'\u28d6'),
    'braille_dots_2358': (0x1002896, u'\u2896'),
    'braille_dots_236': (0x1002826, u'\u2826'),
    'braille_dots_2367': (0x1002866, u'\u2866'),
    'braille_dots_23678': (0x10028e6, u'\u28e6'),
    'braille_dots_2368': (0x10028a6, u'\u28a6'),
    'braille_dots_237': (0x1002846, u'\u2846'),
    'braille_dots_2378': (0x10028c6, u'\u28c6'),
    'braille_dots_238': (0x1002886, u'\u2886'),
    'braille_dots_24': (0x100280a, u'\u280a'),
    'braille_dots_245': (0x100281a, u'\u281a'),
    'braille_dots_2456': (0x100283a, u'\u283a'),
    'braille_dots_24567': (0x100287a, u'\u287a'),
    'braille_dots_245678': (0x10028fa, u'\u28fa'),
    'braille_dots_24568': (0x10028ba, u'\u28ba'),
    'braille_dots_2457': (0x100285a, u'\u285a'),
    'braille_dots_24578': (0x10028da, u'\u28da'),
    'braille_dots_2458': (0x100289a, u'\u289a'),
    'braille_dots_246': (0x100282a, u'\u282a'),
    'braille_dots_2467': (0x100286a, u'\u286a'),
    'braille_dots_24678': (0x10028ea, u'\u28ea'),
    'braille_dots_2468': (0x10028aa, u'\u28aa'),
    'braille_dots_247': (0x100284a, u'\u284a'),
    'braille_dots_2478': (0x10028ca, u'\u28ca'),
    'braille_dots_248': (0x100288a, u'\u288a'),
    'braille_dots_25': (0x1002812, u'\u2812'),
    'braille_dots_256': (0x1002832, u'\u2832'),
    'braille_dots_2567': (0x1002872, u'\u2872'),
    'braille_dots_25678': (0x10028f2, u'\u28f2'),
    'braille_dots_2568': (0x10028b2, u'\u28b2'),
    'braille_dots_257': (0x1002852, u'\u2852'),
    'braille_dots_2578': (0x10028d2, u'\u28d2'),
    'braille_dots_258': (0x1002892, u'\u2892'),
    'braille_dots_26': (0x1002822, u'\u2822'),
    'braille_dots_267': (0x1002862, u'\u2862'),
    'braille_dots_2678': (0x10028e2, u'\u28e2'),
    'braille_dots_268': (0x10028a2, u'\u28a2'),
    'braille_dots_27': (0x1002842, u'\u2842'),
    'braille_dots_278': (0x10028c2, u'\u28c2'),
    'braille_dots_28': (0x1002882, u'\u2882'),
    'braille_dots_3': (0x1002804, u'\u2804'),
    'braille_dots_34': (0x100280c, u'\u280c'),
    'braille_dots_345': (0x100281c, u'\u281c'),
    'braille_dots_3456': (0x100283c, u'\u283c'),
    'braille_dots_34567': (0x100287c, u'\u287c'),
    'braille_dots_345678': (0x10028fc, u'\u28fc'),
    'braille_dots_34568': (0x10028bc, u'\u28bc'),
    'braille_dots_3457': (0x100285c, u'\u285c'),
    'braille_dots_34578': (0x10028dc, u'\u28dc'),
    'braille_dots_3458': (0x100289c, u'\u289c'),
    'braille_dots_346': (0x100282c, u'\u282c'),
    'braille_dots_3467': (0x100286c, u'\u286c'),
    'braille_dots_34678': (0x10028ec, u'\u28ec'),
    'braille_dots_3468': (0x10028ac, u'\u28ac'),
    'braille_dots_347': (0x100284c, u'\u284c'),
    'braille_dots_3478': (0x10028cc, u'\u28cc'),
    'braille_dots_348': (0x100288c, u'\u288c'),
    'braille_dots_35': (0x1002814, u'\u2814'),
    'braille_dots_356': (0x1002834, u'\u2834'),
    'braille_dots_3567': (0x1002874, u'\u2874'),
    'braille_dots_35678': (0x10028f4, u'\u28f4'),
    'braille_dots_3568': (0x10028b4, u'\u28b4'),
    'braille_dots_357': (0x1002854, u'\u2854'),
    'braille_dots_3578': (0x10028d4, u'\u28d4'),
    'braille_dots_358': (0x1002894, u'\u2894'),
    'braille_dots_36': (0x1002824, u'\u2824'),
    'braille_dots_367': (0x1002864, u'\u2864'),
    'braille_dots_3678': (0x10028e4, u'\u28e4'),
    'braille_dots_368': (0x10028a4, u'\u28a4'),
    'braille_dots_37': (0x1002844, u'\u2844'),
    'braille_dots_378': (0x10028c4, u'\u28c4'),
    'braille_dots_38': (0x1002884, u'\u2884'),
    'braille_dots_4': (0x1002808, u'\u2808'),
    'braille_dots_45': (0x1002818, u'\u2818'),
    'braille_dots_456': (0x1002838, u'\u2838'),
    'braille_dots_4567': (0x1002878, u'\u2878'),
    'braille_dots_45678': (0x10028f8, u'\u28f8'),
    'braille_dots_4568': (0x10028b8, u'\u28b8'),
    'braille_dots_457': (0x1002858, u'\u2858'),
    'braille_dots_4578': (0x10028d8, u'\u28d8'),
    'braille_dots_458': (0x1002898, u'\u2898'),
    'braille_dots_46': (0x1002828, u'\u2828'),
    'braille_dots_467': (0x1002868, u'\u2868'),
    'braille_dots_4678': (0x10028e8, u'\u28e8'),
    'braille_dots_468': (0x10028a8, u'\u28a8'),
    'braille_dots_47': (0x1002848, u'\u2848'),
    'braille_dots_478': (0x10028c8, u'\u28c8'),
    'braille_dots_48': (0x1002888, u'\u2888'),
    'braille_dots_5': (0x1002810, u'\u2810'),
    'braille_dots_56': (0x1002830, u'\u2830'),
    'braille_dots_567': (0x1002870, u'\u2870'),
    'braille_dots_5678': (0x10028f0, u'\u28f0'),
    'braille_dots_568': (0x10028b0, u'\u28b0'),
    'braille_dots_57': (0x1002850, u'\u2850'),
    'braille_dots_578': (0x10028d0, u'\u28d0'),
    'braille_dots_58': (0x1002890, u'\u2890'),
    'braille_dots_6': (0x1002820, u'\u2820'),
    'braille_dots_67': (0x1002860, u'\u2860'),
    'braille_dots_678': (0x10028e0, u'\u28e0'),
    'braille_dots_68': (0x10028a0, u'\u28a0'),
    'braille_dots_7': (0x1002840, u'\u2840'),
    'braille_dots_78': (0x10028c0, u'\u28c0'),
    'braille_dots_8': (0x1002880, u'\u2880'),
    'breve': (0x01a2, u'\u02D8'),
    'brokenbar': (0x00a6, u'\u00A6'),
    'c': (0x0063, u'\u0063'),
    'cabovedot': (0x02e5, u'\u010B'),
    'cacute': (0x01e6, u'\u0107'),
    'careof': (0x0ab8, u'\u2105'),
    'caret': (0x0afc, u'\u2038'),
    'caron': (0x01b7, u'\u02C7'),
    'ccaron': (0x01e8, u'\u010D'),
    'ccedilla': (0x00e7, u'\u00E7'),
    'ccircumflex': (0x02e6, u'\u0109'),
    'cedilla': (0x00b8, u'\u00B8'),
    'cent': (0x00a2, u'\u00A2'),
    'checkerboard': (0x09e1, u'\u2592'),
    'checkmark': (0x0af3, u'\u2713'),
    'circle': (0x0bcf, u'\u25CB'),
    'club': (0x0aec, u'\u2663'),
    'colon': (0x003a, u'\u003A'),
    'comma': (0x002c, u'\u002C'),
    'containsas': (0x100220B, u'\u220B'),
    'copyright': (0x00a9, u'\u00A9'),
    'cr': (0x09e4, u'\u240D'),
    'crossinglines': (0x09ee, u'\u253C'),
    'cuberoot': (0x100221B, u'\u221B'),
    'currency': (0x00a4, u'\u00A4'),
    'd': (0x0064, u'\u0064'),
    'dabovedot': (0x1001e0b, u'\u1E0B'),
    'dagger': (0x0af1, u'\u2020'),
    'dcaron': (0x01ef, u'\u010F'),
    'dead_A': (0xfe81, None),
    'dead_E': (0xfe83, None),
    'dead_I': (0xfe85, None),
    'dead_O': (0xfe87, None),
    'dead_U': (0xfe89, None),
    'dead_a': (0xfe80, None),
    'dead_abovecomma': (0xfe64, u'\u0315'),
    'dead_abovedot': (0xfe56, u'\u0307'),
    'dead_abovereversedcomma': (0xfe65, u'\u0312'),
    'dead_abovering': (0xfe58, u'\u030A'),
    'dead_aboveverticalline': (0xfe91, u'\u030D'),
    'dead_acute': (0xfe51, u'\u0301'),
    'dead_belowbreve': (0xfe6b, u'\u032E'),
    'dead_belowcircumflex': (0xfe69, u'\u032D'),
    'dead_belowcomma': (0xfe6e, u'\u0326'),
    'dead_belowdiaeresis': (0xfe6c, u'\u0324'),
    'dead_belowdot': (0xfe60, u'\u0323'),
    'dead_belowmacron': (0xfe68, u'\u0331'),
    'dead_belowring': (0xfe67, u'\u0325'),
    'dead_belowtilde': (0xfe6a, u'\u0330'),
    'dead_belowverticalline': (0xfe92, u'\u0329'),
    'dead_breve': (0xfe55, u'\u0306'),
    'dead_capital_schwa': (0xfe8b, None),
    'dead_caron': (0xfe5a, u'\u030C'),
    'dead_cedilla': (0xfe5b, u'\u0327'),
    'dead_circumflex': (0xfe52, u'\u0302'),
    'dead_currency': (0xfe6f, None),
    'dead_diaeresis': (0xfe57, u'\u0308'),
    'dead_doubleacute': (0xfe59, u'\u030B'),
    'dead_doublegrave': (0xfe66, u'\u030F'),
    'dead_e': (0xfe82, None),
    'dead_grave': (0xfe50, u'\u0300'),
    'dead_greek': (0xfe8c, None),
    'dead_hook': (0xfe61, u'\u0309'),
    'dead_horn': (0xfe62, u'\u031B'),
    'dead_i': (0xfe84, None),
    'dead_invertedbreve': (0xfe6d, u'\u032F'),
    'dead_iota': (0xfe5d, u'\u0345'),
    'dead_longsolidusoverlay': (0xfe93, u'\u0338'),
    'dead_lowline': (0xfe90, u'\u0332'),
    'dead_macron': (0xfe54, u'\u0304'),
    'dead_o': (0xfe86, None),
    'dead_ogonek': (0xfe5c, u'\u0328'),
    'dead_semivoiced_sound': (0xfe5f, None),
    'dead_small_schwa': (0xfe8a, None),
    'dead_stroke': (0xfe63, u'\u0335'),
    'dead_tilde': (0xfe53, u'\u0303'),
    'dead_u': (0xfe88, None),
    'dead_voiced_sound': (0xfe5e, None),
    'degree': (0x00b0, u'\u00B0'),
    'diaeresis': (0x00a8, u'\u00A8'),
    'diamond': (0x0aed, u'\u2666'),
    'digitspace': (0x0aa5, u'\u2007'),
    'dintegral': (0x100222C, u'\u222C'),
    'division': (0x00f7, u'\u00F7'),
    'dollar': (0x0024, u'\u0024'),
    'doubbaselinedot': (0x0aaf, u'\u2025'),
    'doubleacute': (0x01bd, u'\u02DD'),
    'doubledagger': (0x0af2, u'\u2021'),
    'doublelowquotemark': (0x0afe, u'\u201E'),
    'downarrow': (0x08fe, u'\u2193'),
    'downstile': (0x0bc4, u'\u230A'),
    'downtack': (0x0bc2, u'\u22A4'),
    'dstroke': (0x01f0, u'\u0111'),
    'e': (0x0065, u'\u0065'),
    'eabovedot': (0x03ec, u'\u0117'),
    'eacute': (0x00e9, u'\u00E9'),
    'ebelowdot': (0x1001eb9, u'\u1EB9'),
    'ecaron': (0x01ec, u'\u011B'),
    'ecircumflex': (0x00ea, u'\u00EA'),
    'ecircumflexacute': (0x1001ebf, u'\u1EBF'),
    'ecircumflexbelowdot': (0x1001ec7, u'\u1EC7'),
    'ecircumflexgrave': (0x1001ec1, u'\u1EC1'),
    'ecircumflexhook': (0x1001ec3, u'\u1EC3'),
    'ecircumflextilde': (0x1001ec5, u'\u1EC5'),
    'ediaeresis': (0x00eb, u'\u00EB'),
    'egrave': (0x00e8, u'\u00E8'),
    'ehook': (0x1001ebb, u'\u1EBB'),
    'eightsubscript': (0x1002088, u'\u2088'),
    'eightsuperior': (0x1002078, u'\u2078'),
    'elementof': (0x1002208, u'\u2208'),
    'ellipsis': (0x0aae, u'\u2026'),
    'em3space': (0x0aa3, u'\u2004'),
    'em4space': (0x0aa4, u'\u2005'),
    'emacron': (0x03ba, u'\u0113'),
    'emdash': (0x0aa9, u'\u2014'),
    'emptyset': (0x1002205, u'\u2205'),
    'emspace': (0x0aa1, u'\u2003'),
    'endash': (0x0aaa, u'\u2013'),
    'eng': (0x03bf, u'\u014B'),
    'enspace': (0x0aa2, u'\u2002'),
    'eogonek': (0x01ea, u'\u0119'),
    'equal': (0x003d, u'\u003D'),
    'eth': (0x00f0, u'\u00F0'),
    'etilde': (0x1001ebd, u'\u1EBD'),
    'exclam': (0x0021, u'\u0021'),
    'exclamdown': (0x00a1, u'\u00A1'),
    'ezh': (0x1000292, u'\u0292'),
    'f': (0x0066, u'\u0066'),
    'fabovedot': (0x1001e1f, u'\u1E1F'),
    'femalesymbol': (0x0af8, u'\u2640'),
    'ff': (0x09e3, u'\u240C'),
    'figdash': (0x0abb, u'\u2012'),
    'fiveeighths': (0x0ac5, u'\u215D'),
    'fivesixths': (0x0ab7, u'\u215A'),
    'fivesubscript': (0x1002085, u'\u2085'),
    'fivesuperior': (0x1002075, u'\u2075'),
    'fourfifths': (0x0ab5, u'\u2158'),
    'foursubscript': (0x1002084, u'\u2084'),
    'foursuperior': (0x1002074, u'\u2074'),
    'fourthroot': (0x100221C, u'\u221C'),
    'function': (0x08f6, u'\u0192'),
    'g': (0x0067, u'\u0067'),
    'gabovedot': (0x02f5, u'\u0121'),
    'gbreve': (0x02bb, u'\u011F'),
    'gcaron': (0x10001e7, u'\u01E7'),
    'gcedilla': (0x03bb, u'\u0123'),
    'gcircumflex': (0x02f8, u'\u011D'),
    'grave': (0x0060, u'\u0060'),
    'greater': (0x003e, u'\u003E'),
    'greaterthanequal': (0x08be, u'\u2265'),
    'guillemotleft': (0x00ab, u'\u00AB'),
    'guillemotright': (0x00bb, u'\u00BB'),
    'h': (0x0068, u'\u0068'),
    'hairspace': (0x0aa8, u'\u200A'),
    'hcircumflex': (0x02b6, u'\u0125'),
    'heart': (0x0aee, u'\u2665'),
    'hebrew_aleph': (0x0ce0, u'\u05D0'),
    'hebrew_ayin': (0x0cf2, u'\u05E2'),
    'hebrew_bet': (0x0ce1, u'\u05D1'),
    'hebrew_chet': (0x0ce7, u'\u05D7'),
    'hebrew_dalet': (0x0ce3, u'\u05D3'),
    'hebrew_doublelowline': (0x0cdf, u'\u2017'),
    'hebrew_finalkaph': (0x0cea, u'\u05DA'),
    'hebrew_finalmem': (0x0ced, u'\u05DD'),
    'hebrew_finalnun': (0x0cef, u'\u05DF'),
    'hebrew_finalpe': (0x0cf3, u'\u05E3'),
    'hebrew_finalzade': (0x0cf5, u'\u05E5'),
    'hebrew_gimel': (0x0ce2, u'\u05D2'),
    'hebrew_he': (0x0ce4, u'\u05D4'),
    'hebrew_kaph': (0x0ceb, u'\u05DB'),
    'hebrew_lamed': (0x0cec, u'\u05DC'),
    'hebrew_mem': (0x0cee, u'\u05DE'),
    'hebrew_nun': (0x0cf0, u'\u05E0'),
    'hebrew_pe': (0x0cf4, u'\u05E4'),
    'hebrew_qoph': (0x0cf7, u'\u05E7'),
    'hebrew_resh': (0x0cf8, u'\u05E8'),
    'hebrew_samech': (0x0cf1, u'\u05E1'),
    'hebrew_shin': (0x0cf9, u'\u05E9'),
    'hebrew_taw': (0x0cfa, u'\u05EA'),
    'hebrew_tet': (0x0ce8, u'\u05D8'),
    'hebrew_waw': (0x0ce5, u'\u05D5'),
    'hebrew_yod': (0x0ce9, u'\u05D9'),
    'hebrew_zade': (0x0cf6, u'\u05E6'),
    'hebrew_zain': (0x0ce6, u'\u05D6'),
    'horizlinescan1': (0x09ef, u'\u23BA'),
    'horizlinescan3': (0x09f0, u'\u23BB'),
    'horizlinescan5': (0x09f1, u'\u2500'),
    'horizlinescan7': (0x09f2, u'\u23BC'),
    'horizlinescan9': (0x09f3, u'\u23BD'),
    'hstroke': (0x02b1, u'\u0127'),
    'ht': (0x09e2, u'\u2409'),
    'hyphen': (0x00ad, u'\u00AD'),
    'i': (0x0069, u'\u0069'),
    'iacute': (0x00ed, u'\u00ED'),
    'ibelowdot': (0x1001ecb, u'\u1ECB'),
    'ibreve': (0x100012d, u'\u012D'),
    'icircumflex': (0x00ee, u'\u00EE'),
    'identical': (0x08cf, u'\u2261'),
    'idiaeresis': (0x00ef, u'\u00EF'),
    'idotless': (0x02b9, u'\u0131'),
    'ifonlyif': (0x08cd, u'\u21D4'),
    'igrave': (0x00ec, u'\u00EC'),
    'ihook': (0x1001ec9, u'\u1EC9'),
    'imacron': (0x03ef, u'\u012B'),
    'implies': (0x08ce, u'\u21D2'),
    'includedin': (0x08da, u'\u2282'),
    'includes': (0x08db, u'\u2283'),
    'infinity': (0x08c2, u'\u221E'),
    'integral': (0x08bf, u'\u222B'),
    'intersection': (0x08dc, u'\u2229'),
    'iogonek': (0x03e7, u'\u012F'),
    'itilde': (0x03b5, u'\u0129'),
    'j': (0x006a, u'\u006A'),
    'jcircumflex': (0x02bc, u'\u0135'),
    'jot': (0x0bca, u'\u2218'),
    'k': (0x006b, u'\u006B'),
    'kana_A': (0x04b1, u'\u30A2'),
    'kana_CHI': (0x04c1, u'\u30C1'),
    'kana_E': (0x04b4, u'\u30A8'),
    'kana_FU': (0x04cc, u'\u30D5'),
    'kana_HA': (0x04ca, u'\u30CF'),
    'kana_HE': (0x04cd, u'\u30D8'),
    'kana_HI': (0x04cb, u'\u30D2'),
    'kana_HO': (0x04ce, u'\u30DB'),
    'kana_I': (0x04b2, u'\u30A4'),
    'kana_KA': (0x04b6, u'\u30AB'),
    'kana_KE': (0x04b9, u'\u30B1'),
    'kana_KI': (0x04b7, u'\u30AD'),
    'kana_KO': (0x04ba, u'\u30B3'),
    'kana_KU': (0x04b8, u'\u30AF'),
    'kana_MA': (0x04cf, u'\u30DE'),
    'kana_ME': (0x04d2, u'\u30E1'),
    'kana_MI': (0x04d0, u'\u30DF'),
    'kana_MO': (0x04d3, u'\u30E2'),
    'kana_MU': (0x04d1, u'\u30E0'),
    'kana_N': (0x04dd, u'\u30F3'),
    'kana_NA': (0x04c5, u'\u30CA'),
    'kana_NE': (0x04c8, u'\u30CD'),
    'kana_NI': (0x04c6, u'\u30CB'),
    'kana_NO': (0x04c9, u'\u30CE'),
    'kana_NU': (0x04c7, u'\u30CC'),
    'kana_O': (0x04b5, u'\u30AA'),
    'kana_RA': (0x04d7, u'\u30E9'),
    'kana_RE': (0x04da, u'\u30EC'),
    'kana_RI': (0x04d8, u'\u30EA'),
    'kana_RO': (0x04db, u'\u30ED'),
    'kana_RU': (0x04d9, u'\u30EB'),
    'kana_SA': (0x04bb, u'\u30B5'),
    'kana_SE': (0x04be, u'\u30BB'),
    'kana_SHI': (0x04bc, u'\u30B7'),
    'kana_SO': (0x04bf, u'\u30BD'),
    'kana_SU': (0x04bd, u'\u30B9'),
    'kana_TA': (0x04c0, u'\u30BF'),
    'kana_TE': (0x04c3, u'\u30C6'),
    'kana_TO': (0x04c4, u'\u30C8'),
    'kana_TSU': (0x04c2, u'\u30C4'),
    'kana_U': (0x04b3, u'\u30A6'),
    'kana_WA': (0x04dc, u'\u30EF'),
    'kana_WO': (0x04a6, u'\u30F2'),
    'kana_YA': (0x04d4, u'\u30E4'),
    'kana_YO': (0x04d6, u'\u30E8'),
    'kana_YU': (0x04d5, u'\u30E6'),
    'kana_a': (0x04a7, u'\u30A1'),
    'kana_closingbracket': (0x04a3, u'\u300D'),
    'kana_comma': (0x04a4, u'\u3001'),
    'kana_conjunctive': (0x04a5, u'\u30FB'),
    'kana_e': (0x04aa, u'\u30A7'),
    'kana_fullstop': (0x04a1, u'\u3002'),
    'kana_i': (0x04a8, u'\u30A3'),
    'kana_o': (0x04ab, u'\u30A9'),
    'kana_openingbracket': (0x04a2, u'\u300C'),
    'kana_tsu': (0x04af, u'\u30C3'),
    'kana_u': (0x04a9, u'\u30A5'),
    'kana_ya': (0x04ac, u'\u30E3'),
    'kana_yo': (0x04ae, u'\u30E7'),
    'kana_yu': (0x04ad, u'\u30E5'),
    'kcedilla': (0x03f3, u'\u0137'),
    'kra': (0x03a2, u'\u0138'),
    'l': (0x006c, u'\u006C'),
    'lacute': (0x01e5, u'\u013A'),
    'latincross': (0x0ad9, u'\u271D'),
    'lbelowdot': (0x1001e37, u'\u1E37'),
    'lcaron': (0x01b5, u'\u013E'),
    'lcedilla': (0x03b6, u'\u013C'),
    'leftarrow': (0x08fb, u'\u2190'),
    'leftdoublequotemark': (0x0ad2, u'\u201C'),
    'leftmiddlecurlybrace': (0x08af, u'\u23A8'),
    'leftradical': (0x08a1, u'\u23B7'),
    'leftsinglequotemark': (0x0ad0, u'\u2018'),
    'leftt': (0x09f4, u'\u251C'),
    'lefttack': (0x0bdc, u'\u22A3'),
    'less': (0x003c, u'\u003C'),
    'lessthanequal': (0x08bc, u'\u2264'),
    'lf': (0x09e5, u'\u240A'),
    'logicaland': (0x08de, u'\u2227'),
    'logicalor': (0x08df, u'\u2228'),
    'lowleftcorner': (0x09ed, u'\u2514'),
    'lowrightcorner': (0x09ea, u'\u2518'),
    'lstroke': (0x01b3, u'\u0142'),
    'm': (0x006d, u'\u006D'),
    'mabovedot': (0x1001e41, u'\u1E41'),
    'macron': (0x00af, u'\u00AF'),
    'malesymbol': (0x0af7, u'\u2642'),
    'maltesecross': (0x0af0, u'\u2720'),
    'masculine': (0x00ba, u'\u00BA'),
    'minus': (0x002d, u'\u002D'),
    'minutes': (0x0ad6, u'\u2032'),
    'mu': (0x00b5, u'\u00B5'),
    'multiply': (0x00d7, u'\u00D7'),
    'musicalflat': (0x0af6, u'\u266D'),
    'musicalsharp': (0x0af5, u'\u266F'),
    'n': (0x006e, u'\u006E'),
    'nabla': (0x08c5, u'\u2207'),
    'nacute': (0x01f1, u'\u0144'),
    'ncaron': (0x01f2, u'\u0148'),
    'ncedilla': (0x03f1, u'\u0146'),
    'ninesubscript': (0x1002089, u'\u2089'),
    'ninesuperior': (0x1002079, u'\u2079'),
    'nl': (0x09e8, u'\u2424'),
    'nobreakspace': (0x00a0, u'\u00A0'),
    'notapproxeq': (0x1002247, u'\u2247'),
    'notelementof': (0x1002209, u'\u2209'),
    'notequal': (0x08bd, u'\u2260'),
    'notidentical': (0x1002262, u'\u2262'),
    'notsign': (0x00ac, u'\u00AC'),
    'ntilde': (0x00f1, u'\u00F1'),
    'numbersign': (0x0023, u'\u0023'),
    'numerosign': (0x06b0, u'\u2116'),
    'o': (0x006f, u'\u006F'),
    'oacute': (0x00f3, u'\u00F3'),
    'obarred': (0x1000275, u'\u0275'),
    'obelowdot': (0x1001ecd, u'\u1ECD'),
    'ocaron': (0x10001d2, u'\u01D2'),
    'ocircumflex': (0x00f4, u'\u00F4'),
    'ocircumflexacute': (0x1001ed1, u'\u1ED1'),
    'ocircumflexbelowdot': (0x1001ed9, u'\u1ED9'),
    'ocircumflexgrave': (0x1001ed3, u'\u1ED3'),
    'ocircumflexhook': (0x1001ed5, u'\u1ED5'),
    'ocircumflextilde': (0x1001ed7, u'\u1ED7'),
    'odiaeresis': (0x00f6, u'\u00F6'),
    'odoubleacute': (0x01f5, u'\u0151'),
    'oe': (0x13bd, u'\u0153'),
    'ogonek': (0x01b2, u'\u02DB'),
    'ograve': (0x00f2, u'\u00F2'),
    'ohook': (0x1001ecf, u'\u1ECF'),
    'ohorn': (0x10001a1, u'\u01A1'),
    'ohornacute': (0x1001edb, u'\u1EDB'),
    'ohornbelowdot': (0x1001ee3, u'\u1EE3'),
    'ohorngrave': (0x1001edd, u'\u1EDD'),
    'ohornhook': (0x1001edf, u'\u1EDF'),
    'ohorntilde': (0x1001ee1, u'\u1EE1'),
    'omacron': (0x03f2, u'\u014D'),
    'oneeighth': (0x0ac3, u'\u215B'),
    'onefifth': (0x0ab2, u'\u2155'),
    'onehalf': (0x00bd, u'\u00BD'),
    'onequarter': (0x00bc, u'\u00BC'),
    'onesixth': (0x0ab6, u'\u2159'),
    'onesubscript': (0x1002081, u'\u2081'),
    'onesuperior': (0x00b9, u'\u00B9'),
    'onethird': (0x0ab0, u'\u2153'),
    'ooblique': (0x00f8, u'\u00F8'),
    'ordfeminine': (0x00aa, u'\u00AA'),
    'oslash': (0x00f8, u'\u00F8'),
    'otilde': (0x00f5, u'\u00F5'),
    'overline': (0x047e, u'\u203E'),
    'p': (0x0070, u'\u0070'),
    'pabovedot': (0x1001e57, u'\u1E57'),
    'paragraph': (0x00b6, u'\u00B6'),
    'parenleft': (0x0028, u'\u0028'),
    'parenright': (0x0029, u'\u0029'),
    'partdifferential': (0x1002202, u'\u2202'),
    'partialderivative': (0x08ef, u'\u2202'),
    'percent': (0x0025, u'\u0025'),
    'period': (0x002e, u'\u002E'),
    'periodcentered': (0x00b7, u'\u00B7'),
    'permille': (0x0ad5, u'\u2030'),
    'phonographcopyright': (0x0afb, u'\u2117'),
    'plus': (0x002b, u'\u002B'),
    'plusminus': (0x00b1, u'\u00B1'),
    'prescription': (0x0ad4, u'\u211E'),
    'prolongedsound': (0x04b0, u'\u30FC'),
    'punctspace': (0x0aa6, u'\u2008'),
    'q': (0x0071, u'\u0071'),
    'quad': (0x0bcc, u'\u2395'),
    'question': (0x003f, u'\u003F'),
    'questiondown': (0x00bf, u'\u00BF'),
    'quotedbl': (0x0022, u'\u0022'),
    'r': (0x0072, u'\u0072'),
    'racute': (0x01e0, u'\u0155'),
    'radical': (0x08d6, u'\u221A'),
    'rcaron': (0x01f8, u'\u0159'),
    'rcedilla': (0x03b3, u'\u0157'),
    'registered': (0x00ae, u'\u00AE'),
    'rightarrow': (0x08fd, u'\u2192'),
    'rightdoublequotemark': (0x0ad3, u'\u201D'),
    'rightmiddlecurlybrace': (0x08b0, u'\u23AC'),
    'rightsinglequotemark': (0x0ad1, u'\u2019'),
    'rightt': (0x09f5, u'\u2524'),
    'righttack': (0x0bfc, u'\u22A2'),
    's': (0x0073, u'\u0073'),
    'sabovedot': (0x1001e61, u'\u1E61'),
    'sacute': (0x01b6, u'\u015B'),
    'scaron': (0x01b9, u'\u0161'),
    'scedilla': (0x01ba, u'\u015F'),
    'schwa': (0x1000259, u'\u0259'),
    'scircumflex': (0x02fe, u'\u015D'),
    'seconds': (0x0ad7, u'\u2033'),
    'section': (0x00a7, u'\u00A7'),
    'semicolon': (0x003b, u'\u003B'),
    'semivoicedsound': (0x04df, u'\u309C'),
    'seveneighths': (0x0ac6, u'\u215E'),
    'sevensubscript': (0x1002087, u'\u2087'),
    'sevensuperior': (0x1002077, u'\u2077'),
    'similarequal': (0x08c9, u'\u2243'),
    'singlelowquotemark': (0x0afd, u'\u201A'),
    'sixsubscript': (0x1002086, u'\u2086'),
    'sixsuperior': (0x1002076, u'\u2076'),
    'slash': (0x002f, u'\u002F'),
    'soliddiamond': (0x09e0, u'\u25C6'),
    'space': (0x0020, u'\u0020'),
    'squareroot': (0x100221A, u'\u221A'),
    'ssharp': (0x00df, u'\u00DF'),
    'sterling': (0x00a3, u'\u00A3'),
    'stricteq': (0x1002263, u'\u2263'),
    't': (0x0074, u'\u0074'),
    'tabovedot': (0x1001e6b, u'\u1E6B'),
    'tcaron': (0x01bb, u'\u0165'),
    'tcedilla': (0x01fe, u'\u0163'),
    'telephone': (0x0af9, u'\u260E'),
    'telephonerecorder': (0x0afa, u'\u2315'),
    'therefore': (0x08c0, u'\u2234'),
    'thinspace': (0x0aa7, u'\u2009'),
    'thorn': (0x00fe, u'\u00FE'),
    'threeeighths': (0x0ac4, u'\u215C'),
    'threefifths': (0x0ab4, u'\u2157'),
    'threequarters': (0x00be, u'\u00BE'),
    'threesubscript': (0x1002083, u'\u2083'),
    'threesuperior': (0x00b3, u'\u00B3'),
    'tintegral': (0x100222D, u'\u222D'),
    'topintegral': (0x08a4, u'\u2320'),
    'topleftparens': (0x08ab, u'\u239B'),
    'topleftsqbracket': (0x08a7, u'\u23A1'),
    'toprightparens': (0x08ad, u'\u239E'),
    'toprightsqbracket': (0x08a9, u'\u23A4'),
    'topt': (0x09f7, u'\u252C'),
    'trademark': (0x0ac9, u'\u2122'),
    'tslash': (0x03bc, u'\u0167'),
    'twofifths': (0x0ab3, u'\u2156'),
    'twosubscript': (0x1002082, u'\u2082'),
    'twosuperior': (0x00b2, u'\u00B2'),
    'twothirds': (0x0ab1, u'\u2154'),
    'u': (0x0075, u'\u0075'),
    'uacute': (0x00fa, u'\u00FA'),
    'ubelowdot': (0x1001ee5, u'\u1EE5'),
    'ubreve': (0x02fd, u'\u016D'),
    'ucircumflex': (0x00fb, u'\u00FB'),
    'udiaeresis': (0x00fc, u'\u00FC'),
    'udoubleacute': (0x01fb, u'\u0171'),
    'ugrave': (0x00f9, u'\u00F9'),
    'uhook': (0x1001ee7, u'\u1EE7'),
    'uhorn': (0x10001b0, u'\u01B0'),
    'uhornacute': (0x1001ee9, u'\u1EE9'),
    'uhornbelowdot': (0x1001ef1, u'\u1EF1'),
    'uhorngrave': (0x1001eeb, u'\u1EEB'),
    'uhornhook': (0x1001eed, u'\u1EED'),
    'uhorntilde': (0x1001eef, u'\u1EEF'),
    'umacron': (0x03fe, u'\u016B'),
    'underscore': (0x005f, u'\u005F'),
    'union': (0x08dd, u'\u222A'),
    'uogonek': (0x03f9, u'\u0173'),
    'uparrow': (0x08fc, u'\u2191'),
    'upleftcorner': (0x09ec, u'\u250C'),
    'uprightcorner': (0x09eb, u'\u2510'),
    'upstile': (0x0bd3, u'\u2308'),
    'uptack': (0x0bce, u'\u22A5'),
    'uring': (0x01f9, u'\u016F'),
    'utilde': (0x03fd, u'\u0169'),
    'v': (0x0076, u'\u0076'),
    'variation': (0x08c1, u'\u221D'),
    'vertbar': (0x09f8, u'\u2502'),
    'voicedsound': (0x04de, u'\u309B'),
    'vt': (0x09e9, u'\u240B'),
    'w': (0x0077, u'\u0077'),
    'wacute': (0x1001e83, u'\u1E83'),
    'wcircumflex': (0x1000175, u'\u0175'),
    'wdiaeresis': (0x1001e85, u'\u1E85'),
    'wgrave': (0x1001e81, u'\u1E81'),
    'x': (0x0078, u'\u0078'),
    'xabovedot': (0x1001e8b, u'\u1E8B'),
    'y': (0x0079, u'\u0079'),
    'yacute': (0x00fd, u'\u00FD'),
    'ybelowdot': (0x1001ef5, u'\u1EF5'),
    'ycircumflex': (0x1000177, u'\u0177'),
    'ydiaeresis': (0x00ff, u'\u00FF'),
    'yen': (0x00a5, u'\u00A5'),
    'ygrave': (0x1001ef3, u'\u1EF3'),
    'yhook': (0x1001ef7, u'\u1EF7'),
    'ytilde': (0x1001ef9, u'\u1EF9'),
    'z': (0x007a, u'\u007A'),
    'zabovedot': (0x01bf, u'\u017C'),
    'zacute': (0x01bc, u'\u017A'),
    'zcaron': (0x01be, u'\u017E'),
    'zerosubscript': (0x1002080, u'\u2080'),
    'zerosuperior': (0x1002070, u'\u2070'),
    'zstroke': (0x10001b6, u'\u01B6')}

DEAD_KEYS = {
    u'\u0307': u'\u02D9',
    u'\u030A': u'\u02DA',
    u'\u0301': u'\u00B4',
    u'\u0306': u'\u02D8',
    u'\u030C': u'\u02C7',
    u'\u0327': u'\u00B8',
    u'\u0302': u'\u005E',
    u'\u0308': u'\u00A8',
    u'\u030B': u'\u02DD',
    u'\u0300': u'\u0060',
    u'\u0345': u'\u037A',
    u'\u0332': u'\u005F',
    u'\u0304': u'\u00AF',
    u'\u0328': u'\u02DB',
    u'\u0303': u'\u007E'}

KEYPAD_KEYS = {
    'KP_0': 0xffb0,
    'KP_1': 0xffb1,
    'KP_2': 0xffb2,
    'KP_3': 0xffb3,
    'KP_4': 0xffb4,
    'KP_5': 0xffb5,
    'KP_6': 0xffb6,
    'KP_7': 0xffb7,
    'KP_8': 0xffb8,
    'KP_9': 0xffb9,
    'KP_Add': 0xffab,
    'KP_Begin': 0xff9d,
    'KP_Decimal': 0xffae,
    'KP_Delete': 0xff9f,
    'KP_Divide': 0xffaf,
    'KP_Down': 0xff99,
    'KP_End': 0xff9c,
    'KP_Enter': 0xff8d,
    'KP_Equal': 0xffbd,
    'KP_F1': 0xff91,
    'KP_F2': 0xff92,
    'KP_F3': 0xff93,
    'KP_F4': 0xff94,
    'KP_Home': 0xff95,
    'KP_Insert': 0xff9e,
    'KP_Left': 0xff96,
    'KP_Multiply': 0xffaa,
    'KP_Next': 0xff9b,
    'KP_Page_Down': 0xff9b,
    'KP_Page_Up': 0xff9a,
    'KP_Prior': 0xff9a,
    'KP_Right': 0xff98,
    'KP_Separator': 0xffac,
    'KP_Space': 0xff80,
    'KP_Subtract': 0xffad,
    'KP_Tab': 0xff89,
    'KP_Up': 0xff97}

CHARS = {
    codepoint: name
    for name, (keysym, codepoint) in SYMBOLS.items()
    if codepoint}

KEYSYMS = {
    keysym: name
    for name, (keysym, codepoint) in SYMBOLS.items()
    if codepoint}
