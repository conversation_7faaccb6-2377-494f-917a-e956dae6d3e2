CHANGELOG.rst,sha256=i6znhsxRYNwAU1Qy0v5TT7mOGtzQjEGp3Lc2zjn3fCQ,11022
LICENSE,sha256=jOtLnuWt7d5Hsx6XXB2QxzrSe2sWWh3NgMfFRetluQM,35147
berserk-0.13.2.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
berserk-0.13.2.dist-info/LICENSE,sha256=jOtLnuWt7d5Hsx6XXB2QxzrSe2sWWh3NgMfFRetluQM,35147
berserk-0.13.2.dist-info/METADATA,sha256=EYPUFRUhq9cHzDB5DRMIewS0WtTrhtQ1MhdLVJiZLeM,8534
berserk-0.13.2.dist-info/RECORD,,
berserk-0.13.2.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
berserk-0.13.2.dist-info/WHEEL,sha256=7Z8_27uaHI_UZAc4Uox4PpBhQ9Y5_modZXWMxtUi4NU,88
berserk/__init__.py,sha256=lvTRX3dYEd279ipFCklzXaiKPLviIh1uEJdkuKr_AwQ,1059
berserk/__pycache__/__init__.cpython-312.pyc,,
berserk/__pycache__/exceptions.cpython-312.pyc,,
berserk/__pycache__/formats.cpython-312.pyc,,
berserk/__pycache__/models.cpython-312.pyc,,
berserk/__pycache__/session.cpython-312.pyc,,
berserk/__pycache__/utils.cpython-312.pyc,,
berserk/clients/__init__.py,sha256=4cfaFTRrRFI7poonmNPk6lL1p6zT1N7y1Gq5D2-5xRU,4531
berserk/clients/__pycache__/__init__.cpython-312.pyc,,
berserk/clients/__pycache__/account.cpython-312.pyc,,
berserk/clients/__pycache__/analysis.cpython-312.pyc,,
berserk/clients/__pycache__/base.cpython-312.pyc,,
berserk/clients/__pycache__/board.cpython-312.pyc,,
berserk/clients/__pycache__/bots.cpython-312.pyc,,
berserk/clients/__pycache__/broadcasts.cpython-312.pyc,,
berserk/clients/__pycache__/bulk_pairings.cpython-312.pyc,,
berserk/clients/__pycache__/challenges.cpython-312.pyc,,
berserk/clients/__pycache__/external_engine.cpython-312.pyc,,
berserk/clients/__pycache__/games.cpython-312.pyc,,
berserk/clients/__pycache__/messaging.cpython-312.pyc,,
berserk/clients/__pycache__/oauth.cpython-312.pyc,,
berserk/clients/__pycache__/opening_explorer.cpython-312.pyc,,
berserk/clients/__pycache__/puzzles.cpython-312.pyc,,
berserk/clients/__pycache__/relations.cpython-312.pyc,,
berserk/clients/__pycache__/simuls.cpython-312.pyc,,
berserk/clients/__pycache__/studies.cpython-312.pyc,,
berserk/clients/__pycache__/tablebase.cpython-312.pyc,,
berserk/clients/__pycache__/teams.cpython-312.pyc,,
berserk/clients/__pycache__/tournaments.cpython-312.pyc,,
berserk/clients/__pycache__/tv.cpython-312.pyc,,
berserk/clients/__pycache__/users.cpython-312.pyc,,
berserk/clients/account.py,sha256=kro2RVzeN8cUaKElGFKccNHB4hPdFym00h_rbL1t4_g,1741
berserk/clients/analysis.py,sha256=zhDpUvplal4tLsUEGKRCQrXA2TQQR8sgAqp5kkbis7w,1047
berserk/clients/base.py,sha256=FZBoFrYrUjudWug-WVmu_uLaz0b7etUjaYSInss518s,1180
berserk/clients/board.py,sha256=ntt0dSz78wbBcWhCGS6BYQGLopGzrE9P7DyqyMnNLSg,7380
berserk/clients/bots.py,sha256=iFOeW43zy-JPYpEYuJrXyvaAQ6fv47nwHAM5WEuDVo0,3126
berserk/clients/broadcasts.py,sha256=uV3xEnys149ckXsCa3j7elRSXC0xHGHXkt0As85pRwA,7588
berserk/clients/bulk_pairings.py,sha256=Yv56uB92O-6DNpcNIOyf_kxCkSBXcXFrgnlz9SjYqqM,4109
berserk/clients/challenges.py,sha256=oqxPkKFC4qq0CiNa33-Ld_oRMVz8GlT9oocNhaOektM,9200
berserk/clients/external_engine.py,sha256=LcHeUWYeIBceyFKHPvraMGv3rNXcTH35ji4ZWpWZ5go,4064
berserk/clients/games.py,sha256=lrPvO0nN_goI8WD6i5KelT3BfBot-LfPB-q4lXCSHiM,11513
berserk/clients/messaging.py,sha256=LvRV6zFDxlUALNeQ2si7WGeOqj71RTIOOhNNcVkuArQ,421
berserk/clients/oauth.py,sha256=g-xTfyQ3WOkmyM0D0OWbkOpup6EyKm15Gd6QFTxKUjQ,644
berserk/clients/opening_explorer.py,sha256=-QztNJ55yKE8ZXNC3DCTfMfoW-KihV7eau4a8oHUNb4,6810
berserk/clients/puzzles.py,sha256=7P0Vjdxjvy15aKJISEfiQW5cR7neUClOJn0_4pHf7qE,2761
berserk/clients/relations.py,sha256=5D0Z9GL2hHbvzx_5FTeQaUnS6iE4QXc55d5mKxEojhE,910
berserk/clients/simuls.py,sha256=LKydUZXpBvPzCf27dxXC9JX3i25iw9OVXIIUwiEsa7g,381
berserk/clients/studies.py,sha256=Tz8Y6W7Rw18vIbsed2lt8ZOlU4MGZMIhut44bEOyr3c,1160
berserk/clients/tablebase.py,sha256=KIC1ACAcVaHxvEy-MtXsY-yZyVx3oFcmirXCnEscp7c,1922
berserk/clients/teams.py,sha256=ZrR2T2ouVlLDgtzkjcF9KhXw1jKrrcwyzzO3ppW-GTE,4667
berserk/clients/tournaments.py,sha256=iKIxUUeT3fuWsfJyaCrdYR1cVuJJucv66WWq7E9kcxA,20172
berserk/clients/tv.py,sha256=tvXg3_QINeUgYl20dOlh4pF2SPJa_DGFFwiSCrhJhxg,2227
berserk/clients/users.py,sha256=lzL-zV0kyNUyScTBLN2ex9BguO3vbdlUENxDAMkc21w,5926
berserk/exceptions.py,sha256=hzpg7vy1SD0BD-i9ixMnwBg_hBXZaSin6JavbCMuw1o,1763
berserk/formats.py,sha256=FjyvrMI5pA0RqJ91z3EpPR-v08_LQR_fwwldqRXebO4,5366
berserk/models.py,sha256=_4GXlTHvVSmK_m3smyq1WorA-P4cSO9fU1DrNbKZhb0,2381
berserk/py.typed,sha256=sow9soTwP9T_gEAQSVh7Gb8855h04Nwmhs2We-JRgZM,7
berserk/session.py,sha256=InOqicIe1yOQnuViO8RrEKFYOWVjL45KLJMJnzr5VkM,7179
berserk/types/__init__.py,sha256=4I-RR9RNs0ECeFEAMOZvmU-2pAWy74wVIPKXuhnVbQM,1062
berserk/types/__pycache__/__init__.cpython-312.pyc,,
berserk/types/__pycache__/account.cpython-312.pyc,,
berserk/types/__pycache__/analysis.cpython-312.pyc,,
berserk/types/__pycache__/broadcast.cpython-312.pyc,,
berserk/types/__pycache__/bulk_pairings.cpython-312.pyc,,
berserk/types/__pycache__/challenges.cpython-312.pyc,,
berserk/types/__pycache__/common.cpython-312.pyc,,
berserk/types/__pycache__/opening_explorer.cpython-312.pyc,,
berserk/types/__pycache__/puzzles.cpython-312.pyc,,
berserk/types/__pycache__/team.cpython-312.pyc,,
berserk/types/__pycache__/tournaments.cpython-312.pyc,,
berserk/types/account.py,sha256=BSiNbu8TKhKrwja_wjEMU5k3Tc6jHExL0eCM7IkGNrc,1734
berserk/types/analysis.py,sha256=NBeuNXjLROBvcgfxAnUCilyhQB6tKgFIFHrFRoXMx9g,632
berserk/types/broadcast.py,sha256=1s1-9FPLfhBpqwRt4v086oKxXi22eqNctL4PPPpoMLg,424
berserk/types/bulk_pairings.py,sha256=1AgZZQAOXugNStIVXw6MRE98GYVKYCE6upC5Awjme_s,418
berserk/types/challenges.py,sha256=wSQ8-vTIxJ93iKbuen9aBjTXU1veW09SnpxZImTEUq0,1115
berserk/types/common.py,sha256=In0B0oj-Xot776goIjKwQ5cBs3kR6vXaAfiv1SZ6QWo,1893
berserk/types/opening_explorer.py,sha256=HYx3ncQKwWhR83RsA7rKyob3PbzYgYo_vVl-mcW0hkE,2070
berserk/types/puzzles.py,sha256=SKU-K28ACZqEiktB6Q0mf1YOH2PWhd8bIR0hLCM78Mo,175
berserk/types/team.py,sha256=mD-wPYKGTsUt8asBFtQutQ0_jWAOqtlA7MPZTj-wsqk,1144
berserk/types/tournaments.py,sha256=kp-S21SicY59Vq-PjVsJSGYWpF10bUpBiOFJhoFW-rI,1323
berserk/utils.py,sha256=kzbo6WNEpvnTmtDXR340C0u7IksKAWPKTMTWznvO1zQ,4436
