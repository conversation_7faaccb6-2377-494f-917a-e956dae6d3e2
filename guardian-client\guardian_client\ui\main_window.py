"""
Main window for the Guardian Chess Tutor Desktop Client.

This module contains the primary application window with status monitoring
and basic UI components.
"""

import logging
from typing import Optional
from PySide6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
    QPushButton, QLabel, QTextEdit, QStatusBar,
    QGroupBox, QMessageBox
)
from PySide6.QtCore import Qt, QTimer, QThread, Signal
from PySide6.QtGui import QFont, QIcon, QTextCursor
from ..config import ClientConfig
from ..api_client import APIClient, APIClientError

logger = logging.getLogger(__name__)


class StatusCheckWorker(QThread):
    """Worker thread for checking backend status without blocking UI."""
    
    status_checked = Signal(dict)
    error_occurred = Signal(str)
    
    def __init__(self, api_client: APIClient):
        super().__init__()
        self.api_client = api_client
    
    def run(self):
        """Run the status check in a separate thread."""
        try:
            status = self.api_client.get_health_status()
            self.status_checked.emit(status)
        except APIClientError as e:
            self.error_occurred.emit(str(e))


class MainWindow(QMainWindow):
    """
    Main application window for the Guardian Chess Tutor.
    
    This window provides:
    - Backend connection status monitoring
    - Health check functionality
    - Status display and logging
    """
    
    def __init__(self, parent: Optional[QWidget] = None):
        """
        Initialize the main window.
        
        Args:
            parent: Parent widget (optional)
        """
        super().__init__(parent)
        
        # Initialize API client
        self.api_client = APIClient()
        
        # Initialize UI
        self._setup_ui()
        self._setup_status_bar()
        self._setup_connections()
        
        # Initialize status check worker
        self.status_worker: Optional[StatusCheckWorker] = None
        
        logger.info("Main window initialized")
    
    def _setup_ui(self) -> None:
        """Set up the user interface."""
        # Set window properties
        self.setWindowTitle(ClientConfig.WINDOW_TITLE)
        self.setMinimumSize(ClientConfig.WINDOW_WIDTH, ClientConfig.WINDOW_HEIGHT)
        
        # Create central widget and main layout
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        
        # Create header
        header_label = QLabel("Guardian Chess Tutor")
        header_font = QFont()
        header_font.setPointSize(16)
        header_font.setBold(True)
        header_label.setFont(header_font)
        header_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(header_label)
        
        # Create status group
        status_group = self._create_status_group()
        main_layout.addWidget(status_group)
        
        # Create log display
        log_group = self._create_log_group()
        main_layout.addWidget(log_group)
        
        # Add stretch to push everything to the top
        main_layout.addStretch()
    
    def _create_status_group(self) -> QGroupBox:
        """Create the status monitoring group."""
        group = QGroupBox("Backend Status")
        layout = QVBoxLayout(group)
        
        # Status display
        self.status_label = QLabel("Status: Not checked")
        self.status_label.setStyleSheet("QLabel { font-weight: bold; }")
        layout.addWidget(self.status_label)
        
        # Engine info display
        self.engine_label = QLabel("Engine: Unknown")
        layout.addWidget(self.engine_label)
        
        # Connection URL display
        url_label = QLabel(f"Backend URL: {self.api_client.base_url}")
        url_label.setStyleSheet("QLabel { color: gray; font-size: 10pt; }")
        layout.addWidget(url_label)
        
        # Button layout
        button_layout = QHBoxLayout()
        
        # Check status button
        self.check_status_btn = QPushButton("Check Status")
        self.check_status_btn.setMinimumHeight(40)
        button_layout.addWidget(self.check_status_btn)
        
        # Test connection button
        self.test_connection_btn = QPushButton("Test Connection")
        self.test_connection_btn.setMinimumHeight(40)
        button_layout.addWidget(self.test_connection_btn)
        
        layout.addLayout(button_layout)
        
        return group
    
    def _create_log_group(self) -> QGroupBox:
        """Create the log display group."""
        group = QGroupBox("Response Log")
        layout = QVBoxLayout(group)
        
        # Log text area
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setMaximumHeight(200)
        self.log_text.setPlainText("Ready to check backend status...")
        layout.addWidget(self.log_text)
        
        # Clear log button
        clear_btn = QPushButton("Clear Log")
        clear_btn.clicked.connect(self.log_text.clear)
        layout.addWidget(clear_btn)
        
        return group
    
    def _setup_status_bar(self) -> None:
        """Set up the status bar."""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("Ready")
    
    def _setup_connections(self) -> None:
        """Set up signal-slot connections."""
        self.check_status_btn.clicked.connect(self._on_check_status_clicked)
        self.test_connection_btn.clicked.connect(self._on_test_connection_clicked)
    
    def _on_check_status_clicked(self) -> None:
        """Handle check status button click."""
        if self.status_worker and self.status_worker.isRunning():
            return  # Already checking
        
        self.check_status_btn.setEnabled(False)
        self.status_bar.showMessage("Checking backend status...")
        self._log_message("Checking backend status...")
        
        # Create and start worker thread
        self.status_worker = StatusCheckWorker(self.api_client)
        self.status_worker.status_checked.connect(self._on_status_received)
        self.status_worker.error_occurred.connect(self._on_status_error)
        self.status_worker.finished.connect(self._on_status_check_finished)
        self.status_worker.start()
    
    def _on_test_connection_clicked(self) -> None:
        """Handle test connection button click."""
        self.test_connection_btn.setEnabled(False)
        self.status_bar.showMessage("Testing connection...")
        self._log_message("Testing connection...")
        
        # Test connection synchronously (it's fast)
        if self.api_client.test_connection():
            self._log_message("✅ Connection test successful!")
            self.status_bar.showMessage("Connection OK")
        else:
            self._log_message("❌ Connection test failed!")
            self.status_bar.showMessage("Connection failed")
        
        self.test_connection_btn.setEnabled(True)
    
    def _on_status_received(self, status: dict) -> None:
        """Handle successful status response."""
        # Update status display
        service_status = status.get('status', 'unknown')
        self.status_label.setText(f"Status: {service_status}")
        
        if service_status == 'ok':
            self.status_label.setStyleSheet("QLabel { color: green; font-weight: bold; }")
        else:
            self.status_label.setStyleSheet("QLabel { color: orange; font-weight: bold; }")
        
        # Update engine info
        engine_info = status.get('engine', {})
        engine_status = engine_info.get('status', 'unknown')
        
        if engine_status == 'connected':
            info = engine_info.get('info', {})
            engine_name = info.get('name', 'Unknown')
            self.engine_label.setText(f"Engine: {engine_name} (Connected)")
            self.engine_label.setStyleSheet("QLabel { color: green; }")
        else:
            error = engine_info.get('error', 'Unknown error')
            self.engine_label.setText(f"Engine: {error}")
            self.engine_label.setStyleSheet("QLabel { color: red; }")
        
        # Log the full response
        self._log_message("✅ Status check successful!")
        self._log_message(f"Service: {status.get('service', 'Unknown')}")
        self._log_message(f"Version: {status.get('version', 'Unknown')}")
        self._log_message(f"Engine Status: {engine_status}")
        
        self.status_bar.showMessage("Status check completed")
    
    def _on_status_error(self, error_message: str) -> None:
        """Handle status check error."""
        self.status_label.setText("Status: Error")
        self.status_label.setStyleSheet("QLabel { color: red; font-weight: bold; }")
        self.engine_label.setText("Engine: Cannot connect")
        self.engine_label.setStyleSheet("QLabel { color: red; }")
        
        self._log_message(f"❌ Status check failed: {error_message}")
        self.status_bar.showMessage("Status check failed")
    
    def _on_status_check_finished(self) -> None:
        """Handle status check completion."""
        self.check_status_btn.setEnabled(True)
        if self.status_worker:
            self.status_worker.deleteLater()
            self.status_worker = None
    
    def _log_message(self, message: str) -> None:
        """Add a message to the log display."""
        self.log_text.append(f"[{self._get_timestamp()}] {message}")
        
        # Auto-scroll to bottom
        cursor = self.log_text.textCursor()
        cursor.movePosition(QTextCursor.MoveOperation.End)
        self.log_text.setTextCursor(cursor)
    
    def _get_timestamp(self) -> str:
        """Get current timestamp string."""
        from datetime import datetime
        return datetime.now().strftime("%H:%M:%S")
    
    def closeEvent(self, event) -> None:
        """Handle window close event."""
        # Clean up worker thread if running
        if self.status_worker and self.status_worker.isRunning():
            self.status_worker.terminate()
            self.status_worker.wait()
        
        logger.info("Main window closing")
        event.accept()
