"""
Main window for the Guardian Chess Tutor Desktop Client.

This module contains the primary application window with status monitoring,
overlay functionality, and basic UI components.
"""

import logging
from typing import Optional
from PySide6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QPushButton, QLabel, QTextEdit, QStatusBar,
    QGroupBox, QMessageBox, QSlider, QCheckBox
)
from PySide6.QtCore import Qt, QTimer, QThread, Signal, QSettings
from PySide6.QtGui import QFont, QIcon, QTextCursor, QKeySequence
from ..config import ClientConfig
from ..api_client import APIClient, APIClientError
from ..hotkey_manager import HotkeyManager

logger = logging.getLogger(__name__)


class StatusCheckWorker(QThread):
    """Worker thread for checking backend status without blocking UI."""
    
    status_checked = Signal(dict)
    error_occurred = Signal(str)
    
    def __init__(self, api_client: APIClient):
        super().__init__()
        self.api_client = api_client
    
    def run(self):
        """Run the status check in a separate thread."""
        try:
            status = self.api_client.get_health_status()
            self.status_checked.emit(status)
        except APIClientError as e:
            self.error_occurred.emit(str(e))


class MainWindow(QMainWindow):
    """
    Main application window for the Guardian Chess Tutor.
    
    This window provides:
    - Backend connection status monitoring
    - Health check functionality
    - Status display and logging
    """
    
    def __init__(self, parent: Optional[QWidget] = None):
        """
        Initialize the main window.

        Args:
            parent: Parent widget (optional)
        """
        super().__init__(parent)

        # Initialize API client
        self.api_client = APIClient()

        # Initialize overlay state
        self.overlay_enabled = False
        self.original_window_flags = self.windowFlags()
        self.settings = QSettings("Guardian Team", "Guardian Chess Tutor")

        # Initialize UI
        self._setup_ui()
        self._setup_status_bar()
        self._setup_connections()
        self._restore_window_position()

        # Initialize status check worker
        self.status_worker: Optional[StatusCheckWorker] = None

        # Initialize hotkey manager
        self.hotkey_manager = HotkeyManager(self)
        self._setup_hotkey_connections()

        # Start hotkey manager
        if self.hotkey_manager.start():
            self._log_message("🎯 Global hotkeys enabled: Ctrl+Alt+G (Toggle), Ctrl+Alt+E (Enable), Ctrl+Alt+D (Disable)")
        else:
            self._log_message("⚠️ Global hotkeys could not be enabled")

        logger.info("Main window initialized")
    
    def _setup_ui(self) -> None:
        """Set up the user interface."""
        # Set window properties
        self.setWindowTitle(ClientConfig.WINDOW_TITLE)
        self.setMinimumSize(ClientConfig.WINDOW_WIDTH, ClientConfig.WINDOW_HEIGHT)
        
        # Create central widget and main layout
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        
        # Create header
        header_label = QLabel("Guardian Chess Tutor")
        header_font = QFont()
        header_font.setPointSize(16)
        header_font.setBold(True)
        header_label.setFont(header_font)
        header_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(header_label)
        
        # Create status group
        status_group = self._create_status_group()
        main_layout.addWidget(status_group)

        # Create overlay controls group
        overlay_group = self._create_overlay_group()
        main_layout.addWidget(overlay_group)

        # Create log display
        log_group = self._create_log_group()
        main_layout.addWidget(log_group)
        
        # Add stretch to push everything to the top
        main_layout.addStretch()
    
    def _create_status_group(self) -> QGroupBox:
        """Create the status monitoring group."""
        group = QGroupBox("Backend Status")
        layout = QVBoxLayout(group)
        
        # Status display
        self.status_label = QLabel("Status: Not checked")
        self.status_label.setStyleSheet("QLabel { font-weight: bold; }")
        layout.addWidget(self.status_label)
        
        # Engine info display
        self.engine_label = QLabel("Engine: Unknown")
        layout.addWidget(self.engine_label)
        
        # Connection URL display
        url_label = QLabel(f"Backend URL: {self.api_client.base_url}")
        url_label.setStyleSheet("QLabel { color: gray; font-size: 10pt; }")
        layout.addWidget(url_label)
        
        # Button layout
        button_layout = QHBoxLayout()
        
        # Check status button
        self.check_status_btn = QPushButton("Check Status")
        self.check_status_btn.setMinimumHeight(40)
        button_layout.addWidget(self.check_status_btn)
        
        # Test connection button
        self.test_connection_btn = QPushButton("Test Connection")
        self.test_connection_btn.setMinimumHeight(40)
        button_layout.addWidget(self.test_connection_btn)
        
        layout.addLayout(button_layout)
        
        return group

    def _create_overlay_group(self) -> QGroupBox:
        """Create the overlay controls group."""
        group = QGroupBox("Overlay Controls")
        layout = QVBoxLayout(group)

        # Overlay mode buttons
        button_layout = QHBoxLayout()

        self.enable_overlay_btn = QPushButton("Enable Overlay Mode")
        self.enable_overlay_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)

        self.disable_overlay_btn = QPushButton("Disable Overlay Mode")
        self.disable_overlay_btn.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #da190b;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)
        self.disable_overlay_btn.setEnabled(False)

        button_layout.addWidget(self.enable_overlay_btn)
        button_layout.addWidget(self.disable_overlay_btn)
        layout.addLayout(button_layout)

        # Opacity control
        opacity_layout = QHBoxLayout()
        opacity_label = QLabel("Opacity:")
        self.opacity_slider = QSlider(Qt.Horizontal)
        self.opacity_slider.setRange(10, 100)  # 10% to 100%
        self.opacity_slider.setValue(90)  # Default 90%
        self.opacity_value_label = QLabel("90%")

        opacity_layout.addWidget(opacity_label)
        opacity_layout.addWidget(self.opacity_slider)
        opacity_layout.addWidget(self.opacity_value_label)
        layout.addLayout(opacity_layout)

        # Always on top checkbox
        self.always_on_top_cb = QCheckBox("Always on Top")
        self.always_on_top_cb.setChecked(False)
        layout.addWidget(self.always_on_top_cb)

        # Status indicator
        self.overlay_status_label = QLabel("Status: Normal Window Mode")
        self.overlay_status_label.setStyleSheet("QLabel { font-weight: bold; color: #333; }")
        layout.addWidget(self.overlay_status_label)

        # Hotkey information
        hotkey_info = QLabel("Global Hotkeys: Ctrl+Alt+G (Toggle) | Ctrl+Alt+E (Enable) | Ctrl+Alt+D (Disable)")
        hotkey_info.setStyleSheet("QLabel { color: #666; font-size: 9pt; font-style: italic; }")
        hotkey_info.setWordWrap(True)
        layout.addWidget(hotkey_info)

        return group

    def _create_log_group(self) -> QGroupBox:
        """Create the log display group."""
        group = QGroupBox("Response Log")
        layout = QVBoxLayout(group)
        
        # Log text area
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setMaximumHeight(200)
        self.log_text.setPlainText("Ready to check backend status...")
        layout.addWidget(self.log_text)
        
        # Clear log button
        clear_btn = QPushButton("Clear Log")
        clear_btn.clicked.connect(self.log_text.clear)
        layout.addWidget(clear_btn)
        
        return group
    
    def _setup_status_bar(self) -> None:
        """Set up the status bar."""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("Ready")
    
    def _setup_connections(self) -> None:
        """Set up signal-slot connections."""
        self.check_status_btn.clicked.connect(self._on_check_status_clicked)
        self.test_connection_btn.clicked.connect(self._on_test_connection_clicked)

        # Overlay control connections
        self.enable_overlay_btn.clicked.connect(self._enable_overlay_mode)
        self.disable_overlay_btn.clicked.connect(self._disable_overlay_mode)
        self.opacity_slider.valueChanged.connect(self._on_opacity_changed)
        self.always_on_top_cb.toggled.connect(self._on_always_on_top_toggled)

    def _setup_hotkey_connections(self) -> None:
        """Set up global hotkey signal connections."""
        self.hotkey_manager.toggle_overlay_requested.connect(self._toggle_overlay_mode)
        self.hotkey_manager.enable_overlay_requested.connect(self._enable_overlay_mode)
        self.hotkey_manager.disable_overlay_requested.connect(self._disable_overlay_mode)
    
    def _on_check_status_clicked(self) -> None:
        """Handle check status button click."""
        if self.status_worker and self.status_worker.isRunning():
            return  # Already checking
        
        self.check_status_btn.setEnabled(False)
        self.status_bar.showMessage("Checking backend status...")
        self._log_message("Checking backend status...")
        
        # Create and start worker thread
        self.status_worker = StatusCheckWorker(self.api_client)
        self.status_worker.status_checked.connect(self._on_status_received)
        self.status_worker.error_occurred.connect(self._on_status_error)
        self.status_worker.finished.connect(self._on_status_check_finished)
        self.status_worker.start()
    
    def _on_test_connection_clicked(self) -> None:
        """Handle test connection button click."""
        self.test_connection_btn.setEnabled(False)
        self.status_bar.showMessage("Testing connection...")
        self._log_message("Testing connection...")
        
        # Test connection synchronously (it's fast)
        if self.api_client.test_connection():
            self._log_message("✅ Connection test successful!")
            self.status_bar.showMessage("Connection OK")
        else:
            self._log_message("❌ Connection test failed!")
            self.status_bar.showMessage("Connection failed")
        
        self.test_connection_btn.setEnabled(True)

    def _enable_overlay_mode(self) -> None:
        """Enable overlay mode - make window transparent and click-through."""
        try:
            logger.info("Enabling overlay mode")

            # Save current window position
            self._save_window_position()

            # Set overlay window flags
            overlay_flags = (
                Qt.FramelessWindowHint |
                Qt.WindowStaysOnTopHint |
                Qt.WindowTransparentForInput
            )

            # Apply the flags
            self.setWindowFlags(overlay_flags)

            # Set transparency attributes
            self.setAttribute(Qt.WA_TranslucentBackground, True)

            # Apply current opacity
            opacity = self.opacity_slider.value() / 100.0
            self.setWindowOpacity(opacity)

            # Update state
            self.overlay_enabled = True

            # Update UI
            self.enable_overlay_btn.setEnabled(False)
            self.disable_overlay_btn.setEnabled(True)
            self.overlay_status_label.setText("Status: 👻 Ghost Mode Active - Click-through enabled")
            self.overlay_status_label.setStyleSheet("QLabel { font-weight: bold; color: #4CAF50; }")

            # Show the window with new flags
            self.show()

            # Log success
            self._log_message("✅ Overlay mode enabled! Window is now click-through.")
            logger.info("Overlay mode enabled successfully")

        except Exception as e:
            logger.error(f"Failed to enable overlay mode: {e}")
            self._log_message(f"❌ Failed to enable overlay mode: {e}")

    def _toggle_overlay_mode(self) -> None:
        """Toggle overlay mode on/off."""
        if self.overlay_enabled:
            self._disable_overlay_mode()
        else:
            self._enable_overlay_mode()

    def _disable_overlay_mode(self) -> None:
        """Disable overlay mode - restore normal window behavior."""
        try:
            logger.info("Disabling overlay mode")

            # Save current window position
            self._save_window_position()

            # Restore original window flags
            normal_flags = self.original_window_flags
            if self.always_on_top_cb.isChecked():
                normal_flags |= Qt.WindowStaysOnTopHint

            self.setWindowFlags(normal_flags)

            # Remove transparency attributes
            self.setAttribute(Qt.WA_TranslucentBackground, False)

            # Restore full opacity
            self.setWindowOpacity(1.0)

            # Update state
            self.overlay_enabled = False

            # Update UI
            self.enable_overlay_btn.setEnabled(True)
            self.disable_overlay_btn.setEnabled(False)
            self.overlay_status_label.setText("Status: Normal Window Mode")
            self.overlay_status_label.setStyleSheet("QLabel { font-weight: bold; color: #333; }")

            # Show the window with restored flags
            self.show()

            # Log success
            self._log_message("✅ Overlay mode disabled! Window is now interactive.")
            logger.info("Overlay mode disabled successfully")

        except Exception as e:
            logger.error(f"Failed to disable overlay mode: {e}")
            self._log_message(f"❌ Failed to disable overlay mode: {e}")

    def _on_opacity_changed(self, value: int) -> None:
        """Handle opacity slider changes."""
        opacity = value / 100.0
        self.opacity_value_label.setText(f"{value}%")

        if self.overlay_enabled:
            self.setWindowOpacity(opacity)
            logger.debug(f"Opacity changed to {value}%")

    def _on_always_on_top_toggled(self, checked: bool) -> None:
        """Handle always on top checkbox changes."""
        if not self.overlay_enabled:
            # Only apply when not in overlay mode
            flags = self.windowFlags()
            if checked:
                flags |= Qt.WindowStaysOnTopHint
            else:
                flags &= ~Qt.WindowStaysOnTopHint

            self.setWindowFlags(flags)
            self.show()

            status = "enabled" if checked else "disabled"
            self._log_message(f"Always on top {status}")
            logger.info(f"Always on top {status}")

    def _on_status_received(self, status: dict) -> None:
        """Handle successful status response."""
        # Update status display
        service_status = status.get('status', 'unknown')
        self.status_label.setText(f"Status: {service_status}")
        
        if service_status == 'ok':
            self.status_label.setStyleSheet("QLabel { color: green; font-weight: bold; }")
        else:
            self.status_label.setStyleSheet("QLabel { color: orange; font-weight: bold; }")
        
        # Update engine info
        engine_info = status.get('engine', {})
        engine_status = engine_info.get('status', 'unknown')
        
        if engine_status == 'connected':
            info = engine_info.get('info', {})
            engine_name = info.get('name', 'Unknown')
            self.engine_label.setText(f"Engine: {engine_name} (Connected)")
            self.engine_label.setStyleSheet("QLabel { color: green; }")
        else:
            error = engine_info.get('error', 'Unknown error')
            self.engine_label.setText(f"Engine: {error}")
            self.engine_label.setStyleSheet("QLabel { color: red; }")
        
        # Log the full response
        self._log_message("✅ Status check successful!")
        self._log_message(f"Service: {status.get('service', 'Unknown')}")
        self._log_message(f"Version: {status.get('version', 'Unknown')}")
        self._log_message(f"Engine Status: {engine_status}")
        
        self.status_bar.showMessage("Status check completed")
    
    def _on_status_error(self, error_message: str) -> None:
        """Handle status check error."""
        self.status_label.setText("Status: Error")
        self.status_label.setStyleSheet("QLabel { color: red; font-weight: bold; }")
        self.engine_label.setText("Engine: Cannot connect")
        self.engine_label.setStyleSheet("QLabel { color: red; }")
        
        self._log_message(f"❌ Status check failed: {error_message}")
        self.status_bar.showMessage("Status check failed")
    
    def _on_status_check_finished(self) -> None:
        """Handle status check completion."""
        self.check_status_btn.setEnabled(True)
        if self.status_worker:
            self.status_worker.deleteLater()
            self.status_worker = None
    
    def _log_message(self, message: str) -> None:
        """Add a message to the log display."""
        self.log_text.append(f"[{self._get_timestamp()}] {message}")
        
        # Auto-scroll to bottom
        cursor = self.log_text.textCursor()
        cursor.movePosition(QTextCursor.MoveOperation.End)
        self.log_text.setTextCursor(cursor)
    
    def _get_timestamp(self) -> str:
        """Get current timestamp string."""
        from datetime import datetime
        return datetime.now().strftime("%H:%M:%S")
    
    def closeEvent(self, event) -> None:
        """Handle window close event."""
        # Clean up worker thread if running
        if self.status_worker and self.status_worker.isRunning():
            self.status_worker.terminate()
            self.status_worker.wait()

        # Clean up hotkey manager
        if self.hotkey_manager:
            self.hotkey_manager.stop()

        logger.info("Main window closing")
        self._save_window_position()
        event.accept()

    def _save_window_position(self) -> None:
        """Save current window position and size to settings."""
        try:
            self.settings.setValue("window/geometry", self.saveGeometry())
            self.settings.setValue("window/state", self.saveState())
            self.settings.setValue("overlay/opacity", self.opacity_slider.value())
            self.settings.setValue("overlay/always_on_top", self.always_on_top_cb.isChecked())
            logger.debug("Window position saved")
        except Exception as e:
            logger.warning(f"Failed to save window position: {e}")

    def _restore_window_position(self) -> None:
        """Restore window position and size from settings."""
        try:
            geometry = self.settings.value("window/geometry")
            if geometry:
                self.restoreGeometry(geometry)

            state = self.settings.value("window/state")
            if state:
                self.restoreState(state)

            # Restore overlay settings
            opacity = self.settings.value("overlay/opacity", 90, type=int)
            self.opacity_slider.setValue(opacity)
            self.opacity_value_label.setText(f"{opacity}%")

            always_on_top = self.settings.value("overlay/always_on_top", False, type=bool)
            self.always_on_top_cb.setChecked(always_on_top)

            logger.debug("Window position restored")
        except Exception as e:
            logger.warning(f"Failed to restore window position: {e}")

    def _snap_to_edge_if_close(self) -> None:
        """Snap window to screen edge if close enough (magnetic positioning)."""
        try:
            screen = self.screen()
            if not screen:
                return

            screen_geometry = screen.availableGeometry()
            window_geometry = self.geometry()

            snap_distance = 20  # pixels

            # Check left edge
            if abs(window_geometry.left() - screen_geometry.left()) < snap_distance:
                window_geometry.moveLeft(screen_geometry.left())

            # Check right edge
            elif abs(window_geometry.right() - screen_geometry.right()) < snap_distance:
                window_geometry.moveRight(screen_geometry.right())

            # Check top edge
            if abs(window_geometry.top() - screen_geometry.top()) < snap_distance:
                window_geometry.moveTop(screen_geometry.top())

            # Check bottom edge
            elif abs(window_geometry.bottom() - screen_geometry.bottom()) < snap_distance:
                window_geometry.moveBottom(screen_geometry.bottom())

            # Apply the snapped position
            if window_geometry != self.geometry():
                self.setGeometry(window_geometry)
                logger.debug("Window snapped to screen edge")

        except Exception as e:
            logger.warning(f"Failed to snap window to edge: {e}")

    def moveEvent(self, event) -> None:
        """Handle window move events for edge snapping."""
        super().moveEvent(event)
        if not self.overlay_enabled:
            # Only snap when not in overlay mode
            self._snap_to_edge_if_close()
