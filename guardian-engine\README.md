# Guardian Chess Tutor Backend Engine

A backend service for analyzing chess positions using the Stockfish chess engine via the Universal Chess Interface (UCI) protocol.

## Features

- Flask-based REST API
- UCI communication with Stockfish engine
- Health check endpoint
- Structured logging
- Environment-based configuration

## Requirements

- Python 3.8.1+
- Stockfish chess engine (installed separately)
- Poetry for dependency management

## Installation

### 1. Install Stockfish Chess Engine

**Windows:**
1. Download Stockfish from [stockfishchess.org](https://stockfishchess.org/download/)
2. Extract to a folder (e.g., `C:\Program Files\Stockfish\`)
3. Note the path to `stockfish.exe`

**macOS:**
```bash
brew install stockfish
```

**Linux (Ubuntu/Debian):**
```bash
sudo apt-get update
sudo apt-get install stockfish
```

**Linux (CentOS/RHEL):**
```bash
sudo yum install stockfish
```

### 2. Install Python Dependencies

Ensure you have Python 3.8.1+ and Poetry installed:

```bash
# Install Poetry if not already installed
curl -sSL https://install.python-poetry.org | python3 -

# Install project dependencies
poetry install
```

### 3. Configure Environment Variables

Create a `.env` file in the project root or set environment variables:

```bash
# Required: Path to Stockfish executable
export STOCKFISH_PATH=/usr/local/bin/stockfish  # Linux/macOS
# export STOCKFISH_PATH="C:\Program Files\Stockfish\stockfish.exe"  # Windows

# Optional: Lichess API integration
export LICHESS_PAT=your_personal_access_token  # Get from https://lichess.org/account/oauth/token

# Optional: Flask configuration
export FLASK_ENV=development
export FLASK_HOST=127.0.0.1
export FLASK_PORT=5000
export UCI_TIMEOUT=30
export LOG_LEVEL=INFO
```

**Note:** If Stockfish is in your system PATH, you can use:
- `export STOCKFISH_PATH=stockfish` (Linux/macOS)
- `export STOCKFISH_PATH=stockfish.exe` (Windows)

### Lichess API Setup (Optional)

To enable Lichess integration features:

1. **Create a Personal Access Token:**
   - Go to [https://lichess.org/account/oauth/token](https://lichess.org/account/oauth/token)
   - Click "Create a new personal access token"
   - Select scopes: `board:play`, `game:read` (minimum required)
   - Copy the generated token

2. **Set the environment variable:**
   ```bash
   export LICHESS_PAT=your_token_here
   ```

3. **Verify the integration:**
   ```bash
   curl http://localhost:5000/lichess/account
   ```

**Security Note:** Keep your PAT secure and never commit it to version control.

### Event Streaming (Real-time Game Detection)

When a valid `LICHESS_PAT` is configured, Guardian automatically connects to the Lichess event stream to monitor for live games:

**Automatic Features:**
- **Real-time game detection** - Detects when you start a new game on Lichess
- **Background monitoring** - Runs in a separate thread without blocking the web server
- **Resilient connection** - Automatically reconnects on network failures
- **Comprehensive logging** - Logs game starts, finishes, and other events

**Event Types Monitored:**
- `gameStart` - New game detection (primary focus for future analysis)
- `gameFinish` - Game completion
- `challenge` - Incoming challenges
- `challengeCanceled` - Challenge cancellations
- `challengeDeclined` - Challenge rejections

**Example Log Output:**
```
🎮 New game started! Game ID: EfjKilid
   Opponent: Melania97
   Playing as: black
   Time control: rapid
   Rated: Yes
🎯 Guardian detected new game: EfjKilid
```

### Real-Time Game Analysis

When games are detected, Guardian automatically:

**Live Game Streaming:**
- **Individual game monitoring** - Connects to each game's stream (`/api/board/game/stream/{gameId}`)
- **Board state tracking** - Maintains accurate position using python-chess
- **Move-by-move analysis** - Analyzes every position in real-time
- **Multi-threaded processing** - Handles multiple concurrent games
- **Mid-game startup support** - Can safely start monitoring games already in progress

**Analysis Integration:**
- **Automatic position analysis** - Uses the `/analyze` endpoint for each move
- **Multi-PV evaluation** - Shows top 3 moves for each position
- **Comprehensive logging** - Detailed analysis results for every move

**Example Analysis Output:**
```
[GameID] 🧠 Analyzing position after move 5: e7e5
[GameID] 📊 Analysis complete. Best move: g1f3, Eval: 0.25 pawns
[GameID] 🔍 Top variations:
[GameID]   1. g1f3 (0.25 pawns)
[GameID]   2. d2d4 (0.20 pawns)
[GameID]   3. f2f4 (0.15 pawns)
```

### Tutor Brain Intelligence

The Guardian Chess Tutor features an intelligent "Tutor Brain" that analyzes positions for learning opportunities:

**Critical Moment Detection:**
- **Evaluation gap analysis** - Compares best move vs alternatives using multi-PV data
- **Configurable thresholds** - Default 150 centipawns, customizable via `TACTIC_THRESHOLD`
- **Moment classification** - Tactical opportunity, significant advantage, major tactical opportunity
- **Educational advice** - Contextual guidance based on position type

**Intelligence Features:**
- **Real-time analysis** - Every position analyzed for critical moments during live games
- **Mate vs material** - Proper handling of mate scores vs centipawn evaluations
- **Robust evaluation** - Multiple fallback methods for different score types
- **Learning insights** - Identifies when tactical opportunities are available

**Blunder Detection:**
- **Post-move analysis** - Analyzes user moves using Centipawn Loss (CPL) calculation
- **Severity classification** - Inaccuracy (100-199cp), Mistake (200-299cp), Blunder (300-499cp), Major Blunder (500cp+)
- **Educational feedback** - Provides specific advice and shows missed opportunities
- **Unanalyzed moves** - Handles moves not in top variations with appropriate penalties

**Contextual State-Change Filter:**
- **Game state classification** - Categorizes positions as WINNING, EQUAL, or LOSING
- **Critical vs harmless distinction** - Only flags blunders that change game outcome
- **State change detection** - Identifies negative transitions (WINNING→EQUAL, WINNING→LOSING, EQUAL→LOSING)
- **Contextual wisdom** - Understands that a 300cp loss in a +10 position is different from one in an equal position

**Example Intelligence Output:**
```
[GameID] 🧠 TUTOR BRAIN: CRITICAL MOMENT DETECTED!
[GameID] 🎯 Type: Major Tactical Opportunity
[GameID] ⚡ Evaluation gap: 775 centipawns (7.8 pawns)
[GameID] 🎓 Best move: Qxh7+
[GameID] 💡 Advice: This is a major tactical opportunity!

[GameID] 🧠 TUTOR BRAIN: CRITICAL BLUNDER DETECTED!
[GameID] ⚠️  Game state change: winning → losing
[GameID] 💥 Severity: Major Blunder
[GameID] 📉 Centipawn Loss: 1200 (12.0 pawns)
[GameID] 🎯 Best move was: Qh7#
[GameID] 💡 Contextual advice: CRITICAL BLUNDER! Turned a winning position into a losing one.

[GameID] 🧠 TUTOR BRAIN: BLUNDER DETECTED!
[GameID] ℹ️  Game state unchanged: winning
[GameID] 💥 Severity: Mistake
[GameID] 📉 Centipawn Loss: 300 (3.0 pawns)
[GameID] 💡 Contextual advice: Inaccuracy noted, but position remains winning.

[GameID] 🧠 TUTOR BRAIN: Position is quiet.
[GameID] 🧠 TUTOR BRAIN: Solid move. Position: equal
```

This provides the foundation for advanced features like live move suggestions, blunder detection, and real-time tutoring.

## Usage

### Starting the Server

**Method 1: Using Poetry (Recommended)**
```bash
poetry run flask --app guardian_engine.app run
```

**Method 2: Using Python module**
```bash
poetry run python -m guardian_engine.app
```

**Method 3: Direct execution**
```bash
poetry shell
python guardian_engine/app.py
```

The server will start on `http://localhost:5000` by default.

### Testing the Installation

Once the server is running, test the health endpoint:

```bash
curl http://localhost:5000/health
```

Expected response:
```json
{
  "status": "ok",
  "service": "Guardian Chess Tutor Backend",
  "version": "0.1.0",
  "engine": {
    "status": "connected",
    "info": {
      "name": "Stockfish 17.1",
      "author": "the Stockfish developers",
      "path": "/usr/local/bin/stockfish"
    }
  }
}
```

Test the analysis endpoint:

```bash
curl -X POST http://localhost:5000/analyze \
  -H 'Content-Type: application/json' \
  -d '{"fen": "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1"}'
```

Test the Lichess integration (if PAT configured):

```bash
# Get account information
curl http://localhost:5000/lichess/account

# Check event stream status
curl http://localhost:5000/lichess/stream/status
```

Expected response:
```json
{
  "status": "success",
  "data": {
    "bestMove": "e2e4",
    "evaluation": {
      "type": "centipawns",
      "value": 36,
      "description": "0.36 pawns"
    },
    "depth": 24,
    "time": 2.0,
    "fen": "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1"
  }
}
```

## API Endpoints

### Health Check
- **GET** `/health` - Returns service and engine status
  - **Response**: JSON object with service and engine information
  - **Status Codes**:
    - `200` - Service is healthy
    - `500` - Service or engine error

### Chess Position Analysis
- **POST** `/analyze` - Analyze a chess position and get the best move
  - **Request Body**: JSON object with the following fields:
    - `fen` (required): FEN string representing the chess position
    - `time` (optional): Analysis time in seconds (default: 2.0, max: 30.0)
    - `multiPv` (optional): Number of principal variations to analyze (default: 1, max: 10)
  - **Response**: JSON object with analysis results
  - **Status Codes**:
    - `200` - Analysis successful
    - `400` - Invalid request (bad FEN, missing fields, etc.)
    - `500` - Analysis failed or engine error
    - `503` - Engine not available

### Lichess API Integration
- **GET** `/lichess/account` - Get authenticated user's Lichess account information
  - **Authentication**: Requires `LICHESS_PAT` environment variable
  - **Response**: JSON object with account details and user status
  - **Status Codes**:
    - `200` - Account information retrieved successfully
    - `401` - Authentication failed (invalid PAT)
    - `503` - Lichess integration not configured

- **GET** `/lichess/stream/status` - Get Lichess event stream status
  - **Authentication**: Requires `LICHESS_PAT` environment variable
  - **Response**: JSON object with stream status and connection details
  - **Status Codes**:
    - `200` - Stream status retrieved successfully
    - `503` - Lichess integration not configured

#### Example Requests

**Single Best Move Analysis:**
```bash
curl -X POST http://localhost:5000/analyze \
  -H 'Content-Type: application/json' \
  -d '{
    "fen": "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1",
    "time": 2.0
  }'
```

**Multi-PV Analysis (Top 3 Moves):**
```bash
curl -X POST http://localhost:5000/analyze \
  -H 'Content-Type: application/json' \
  -d '{
    "fen": "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1",
    "time": 2.0,
    "multiPv": 3
  }'
```

#### Example Responses

**Single PV Response:**
```json
{
  "status": "success",
  "data": {
    "bestMove": "e2e4",
    "evaluation": {
      "type": "centipawns",
      "value": 36,
      "description": "0.36 pawns"
    },
    "depth": 24,
    "time": 2.0,
    "fen": "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1",
    "analysis": [
      {
        "move": "e2e4",
        "evaluation": {
          "type": "centipawns",
          "value": 36,
          "description": "0.36 pawns"
        },
        "depth": 24,
        "pv": ["e2e4", "e7e5", "g1f3", "b8c6"]
      }
    ]
  }
}
```

**Multi-PV Response (3 variations):**
```json
{
  "status": "success",
  "data": {
    "bestMove": "e2e4",
    "evaluation": {
      "type": "centipawns",
      "value": 34,
      "description": "0.34 pawns"
    },
    "depth": 21,
    "time": 2.0,
    "fen": "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1",
    "multiPv": 3,
    "analysis": [
      {
        "move": "e2e4",
        "evaluation": {
          "type": "centipawns",
          "value": 34,
          "description": "0.34 pawns"
        },
        "depth": 21,
        "pv": ["e2e4", "e7e5"],
        "multipv": 1
      },
      {
        "move": "d2d4",
        "evaluation": {
          "type": "centipawns",
          "value": 26,
          "description": "0.26 pawns"
        },
        "depth": 20,
        "pv": ["d2d4", "g8f6", "c2c4"],
        "multipv": 2
      },
      {
        "move": "g1f3",
        "evaluation": {
          "type": "centipawns",
          "value": 20,
          "description": "0.20 pawns"
        },
        "depth": 18,
        "pv": ["g1f3", "d7d5", "d2d4"],
        "multipv": 3
      }
    ]
  }
}
```

#### Example Response (Error)
```json
{
  "error": "Invalid FEN: expected 8 rows in position part of fen",
  "status": "error",
  "code": 400
}
```

### Lichess Account Response Example

```bash
curl http://localhost:5000/lichess/account
```

**Success Response:**
```json
{
  "status": "success",
  "data": {
    "account": {
      "username": "your_username",
      "title": "GM",
      "online": true,
      "patron": false,
      "verified": true,
      "profile": {
        "country": "US",
        "location": "New York",
        "bio": "Chess enthusiast"
      },
      "perfs": {
        "bullet": {"rating": 2100, "games": 500},
        "blitz": {"rating": 2200, "games": 800},
        "rapid": {"rating": 2150, "games": 300}
      }
    },
    "status": {
      "authenticated": true,
      "username": "your_username",
      "online": true
    }
  }
}
```

**Error Response (No PAT):**
```json
{
  "error": "Lichess integration not configured. Please set LICHESS_PAT environment variable.",
  "status": "error",
  "code": 503
}
```

## Development

### Running Tests
```bash
poetry run pytest
```

### Code Formatting
```bash
poetry run black .
```

### Code Linting
```bash
poetry run flake8
```

### Development Server with Auto-reload
```bash
export FLASK_ENV=development
poetry run flask --app guardian_engine.app run --debug
```

## Troubleshooting

### Common Issues

**1. "Stockfish executable not found"**
- Verify Stockfish is installed: `stockfish` (Linux/macOS) or `stockfish.exe` (Windows)
- Check the `STOCKFISH_PATH` environment variable
- Ensure the path is correct and the file is executable

**2. "UCI handshake failed"**
- Verify Stockfish version compatibility (Stockfish 14+ recommended)
- Check if another process is using the engine
- Try running Stockfish manually to verify it works

**3. "Permission denied" errors**
- Ensure Stockfish executable has proper permissions
- On Linux/macOS: `chmod +x /path/to/stockfish`

**4. Port already in use**
- Change the port: `export FLASK_PORT=5001`
- Or kill the process using the port: `lsof -ti:5000 | xargs kill`

### Logging

Enable debug logging for troubleshooting:
```bash
export LOG_LEVEL=DEBUG
poetry run python guardian_engine/app.py
```

### Verifying Stockfish Installation

Test Stockfish directly:
```bash
# Linux/macOS
echo "uci" | stockfish

# Windows
echo uci | stockfish.exe
```

Expected output should include:
```
id name Stockfish 16
id author the Stockfish developers
uciok
```

## Architecture

The Guardian Engine follows a modular architecture:

- **`app.py`** - Flask application factory and main entry point
- **`config.py`** - Environment-based configuration management
- **`uci_manager.py`** - UCI protocol communication with Stockfish
- **`__init__.py`** - Package initialization

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests and linting
5. Submit a pull request

## License

This project is part of the Guardian Chess Tutor system.
