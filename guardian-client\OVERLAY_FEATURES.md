# Guardian Chess Tutor - Overlay Features

## 🎯 Overview

The Guardian Chess Tutor now includes a complete **Professional Overlay System** that transforms the desktop client into a "ghost-like" overlay that can float over chess applications without interfering with gameplay.

## ✨ Features Implemented

### 1. **Transparent Always-on-Top Overlay Window**
- **Frameless Window**: Removes window borders and title bar
- **Click-Through Mode**: Mouse clicks pass through to applications underneath
- **Always on Top**: Window stays above all other applications
- **Adjustable Transparency**: 10% to 100% opacity control

### 2. **Professional UI Controls**
- **Enable Overlay Mode** button (Green) - Activates ghost mode
- **Disable Overlay Mode** button (Red) - Returns to normal window
- **Opacity Slider** - Real-time transparency adjustment
- **Always on Top Checkbox** - Toggle stay-on-top behavior
- **Visual Status Indicator** - Shows current mode with prominent feedback

### 3. **Global Hotkeys (System-wide)**
- **Ctrl+Alt+G** - Toggle overlay mode on/off
- **Ctrl+Alt+E** - Enable overlay mode
- **Ctrl+Alt+D** - Disable overlay mode
- Works from any application, even when Guardian is not focused

### 4. **Position Memory & Persistence**
- **Window Position Saving** - Remembers location between sessions
- **Settings Persistence** - Opacity and preferences saved automatically
- **State Restoration** - Restores window geometry on startup

### 5. **Snap-to-Edges Functionality**
- **Magnetic Positioning** - Window snaps to screen edges when dragged close
- **20-pixel Snap Distance** - Smooth, responsive edge detection
- **All Edges Supported** - Top, bottom, left, and right edge snapping

### 6. **Enhanced User Experience**
- **Prominent Visual Cues** - Clear status indicators and styled buttons
- **Real-time Feedback** - Immediate response to all controls
- **Comprehensive Logging** - Detailed activity logging for troubleshooting
- **Error Handling** - Graceful handling of edge cases

## 🚀 How to Use

### Basic Operation
1. **Launch Guardian Client**: `python -m guardian_client`
2. **Enable Overlay**: Click "Enable Overlay Mode" or press `Ctrl+Alt+E`
3. **Adjust Transparency**: Use the opacity slider (10-100%)
4. **Position Window**: Drag to desired location (snaps to edges)
5. **Disable Overlay**: Click "Disable Overlay Mode" or press `Ctrl+Alt+D`

### Global Hotkeys
- **Toggle Mode**: `Ctrl+Alt+G` - Switch between normal and overlay modes
- **Enable Overlay**: `Ctrl+Alt+E` - Activate ghost mode
- **Disable Overlay**: `Ctrl+Alt+D` - Return to normal window

### Advanced Features
- **Always on Top**: Check the checkbox to keep window above others
- **Position Memory**: Window remembers its position between sessions
- **Edge Snapping**: Drag window near screen edges for magnetic positioning

## 🛠️ Technical Implementation

### Core Technologies
- **PySide6/Qt6**: Modern GUI framework with advanced window management
- **pynput**: Global hotkey system for system-wide keyboard shortcuts
- **QSettings**: Persistent configuration and position memory

### Key Qt Features Used
- `Qt.FramelessWindowHint` - Removes window frame
- `Qt.WindowStaysOnTopHint` - Always on top behavior
- `Qt.WindowTransparentForInput` - Click-through functionality
- `Qt.WA_TranslucentBackground` - Window transparency
- `setWindowOpacity()` - Adjustable transparency levels

### Architecture
- **MainWindow**: Core overlay functionality and UI controls
- **HotkeyManager**: Global hotkey registration and handling
- **Settings Management**: Position and preference persistence
- **Edge Snapping**: Magnetic window positioning system

## 🎮 Testing

Run the comprehensive test suite:
```bash
python test_overlay.py
```

This will launch the Guardian Client with detailed testing instructions for all overlay features.

## 🔧 Configuration

### Default Settings
- **Opacity**: 90% (adjustable 10-100%)
- **Hotkeys**: Ctrl+Alt+G/E/D (system-wide)
- **Snap Distance**: 20 pixels
- **Always on Top**: Disabled by default

### Customization
All settings are automatically saved and restored:
- Window position and size
- Opacity preference
- Always on top setting
- Last used configuration

## 🎯 Success Criteria Met

✅ **Borderless Always-on-Top Window** - Implemented with Qt window flags  
✅ **Click-Through Functionality** - Mouse events pass to underlying apps  
✅ **Enable/Disable Toggle** - Instant mode switching with visual feedback  
✅ **Professional UI** - Styled buttons, sliders, and status indicators  
✅ **Global Hotkeys** - System-wide keyboard shortcuts  
✅ **Position Memory** - Persistent window positioning  
✅ **Snap-to-Edges** - Magnetic window positioning  
✅ **Windows Compatibility** - Optimized for Windows desktop environment  

## 🎉 Result

The Guardian Chess Tutor now functions as a true "ghost" overlay that can float transparently over chess applications, providing helpful guidance without interfering with gameplay. Users can seamlessly toggle between normal window mode and ghost mode using either UI controls or global hotkeys, creating an unobtrusive yet powerful chess tutoring experience.
