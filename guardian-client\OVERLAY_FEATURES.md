# Guardian Chess Tutor - Overlay Features

## 🎯 Overview

The Guardian Chess Tutor now includes a complete **Professional Overlay System** that transforms the desktop client into a "ghost-like" overlay that can float over chess applications without interfering with gameplay.

## ✨ Features Implemented

### 1. **Transparent Always-on-Top Overlay Window**
- **Frameless Window**: Removes window borders and title bar in overlay mode
- **Always on Top**: Window automatically stays above all other applications in overlay mode
- **Adjustable Transparency**: 10% to 100% opacity control
- **Minimal UI**: Only essential controls remain visible in overlay mode

### 2. **Simplified UI Controls**
- **Single Toggle Button** - Changes between "Enable Overlay Mode" (Green) and "Disable Overlay Mode" (Red)
- **Opacity Slider** - Real-time transparency adjustment
- **Visual Status Indicator** - Shows current mode with prominent feedback
- **Always Visible Toggle** - But<PERSON> remains clickable even in overlay mode

### 3. **Global Hotkey (System-wide)**
- **Ctrl+Alt+G** - Toggle overlay mode on/off
- Works from any application, even when Guardian is not focused
- Single, simple hotkey for all overlay control

### 4. **Position Memory & Persistence**
- **Window Position Saving** - Remembers location between sessions
- **Settings Persistence** - Opacity and preferences saved automatically
- **State Restoration** - Restores window geometry on startup

### 5. **Snap-to-Edges Functionality**
- **Magnetic Positioning** - Window snaps to screen edges when dragged close
- **20-pixel Snap Distance** - Smooth, responsive edge detection
- **All Edges Supported** - Top, bottom, left, and right edge snapping

### 6. **Enhanced User Experience**
- **Prominent Visual Cues** - Clear status indicators and styled buttons
- **Real-time Feedback** - Immediate response to all controls
- **Comprehensive Logging** - Detailed activity logging for troubleshooting
- **Error Handling** - Graceful handling of edge cases

## 🚀 How to Use

### Basic Operation
1. **Launch Guardian Client**: `python -m guardian_client`
2. **Toggle Overlay**: Click the toggle button or press `Ctrl+Alt+G`
3. **Adjust Transparency**: Use the opacity slider (10-100%) before enabling overlay
4. **Position Window**: Drag to desired location (snaps to edges in normal mode)
5. **Exit Overlay**: Click the red "Disable Overlay Mode" button or press `Ctrl+Alt+G`

### Global Hotkey
- **Toggle Mode**: `Ctrl+Alt+G` - Switch between normal and overlay modes
- Works from any application, even when Guardian is not focused

### Advanced Features
- **Automatic Always on Top**: Overlay mode automatically keeps window above others
- **Position Memory**: Window remembers its position between sessions
- **Edge Snapping**: Drag window near screen edges for magnetic positioning (normal mode)
- **Persistent Toggle Button**: Button remains clickable even in overlay mode

## 🛠️ Technical Implementation

### Core Technologies
- **PySide6/Qt6**: Modern GUI framework with advanced window management
- **pynput**: Global hotkey system for system-wide keyboard shortcuts
- **QSettings**: Persistent configuration and position memory

### Key Qt Features Used
- `Qt.FramelessWindowHint` - Removes window frame in overlay mode
- `Qt.WindowStaysOnTopHint` - Always on top behavior in overlay mode
- `Qt.WA_TranslucentBackground` - Window transparency
- `setWindowOpacity()` - Adjustable transparency levels
- Dynamic UI hiding/showing for minimal overlay interface

### Architecture
- **MainWindow**: Core overlay functionality with simplified toggle interface
- **HotkeyManager**: Single global hotkey registration and handling
- **Settings Management**: Position and preference persistence
- **Edge Snapping**: Magnetic window positioning system
- **UI State Management**: Dynamic hiding/showing of interface elements

## 🎮 Testing

Run the comprehensive test suite:
```bash
python test_overlay.py
```

This will launch the Guardian Client with detailed testing instructions for all overlay features.

## 🔧 Configuration

### Default Settings
- **Opacity**: 90% (adjustable 10-100%)
- **Hotkey**: Ctrl+Alt+G (system-wide toggle)
- **Snap Distance**: 20 pixels
- **Always on Top**: Automatic in overlay mode

### Customization
All settings are automatically saved and restored:
- Window position and size
- Opacity preference
- Last used configuration

## 🎯 Success Criteria Met

✅ **Borderless Always-on-Top Window** - Implemented with Qt window flags in overlay mode
✅ **Single Toggle Interface** - One button and one hotkey for all overlay control
✅ **Simplified User Experience** - Clean, minimal interface with essential controls only
✅ **Always Visible Toggle** - Button remains clickable even in overlay mode
✅ **Professional UI** - Styled toggle button with clear visual feedback
✅ **Global Hotkey** - Single system-wide keyboard shortcut (Ctrl+Alt+G)
✅ **Position Memory** - Persistent window positioning
✅ **Snap-to-Edges** - Magnetic window positioning
✅ **Windows Compatibility** - Optimized for Windows desktop environment

## 🎉 Result

The Guardian Chess Tutor now functions as a true "ghost" overlay that can float transparently over chess applications, providing helpful guidance without interfering with gameplay. Users can seamlessly toggle between normal window mode and ghost mode using either UI controls or global hotkeys, creating an unobtrusive yet powerful chess tutoring experience.
