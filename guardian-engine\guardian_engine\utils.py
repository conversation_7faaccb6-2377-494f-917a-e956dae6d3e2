"""
Utility functions for Guardian Chess Tutor Backend.

Contains helper functions for FEN validation, request processing, and other common tasks.
"""

import logging
import chess
from typing import Dict, Any, Optional, Tuple

logger = logging.getLogger(__name__)

def validate_fen(fen: str) -> Tuple[bool, Optional[str]]:
    """
    Validate a FEN string using python-chess.
    
    Args:
        fen: FEN string to validate
        
    Returns:
        Tuple of (is_valid, error_message)
        - is_valid: True if FEN is valid, False otherwise
        - error_message: None if valid, error description if invalid
    """
    if not fen or not isinstance(fen, str):
        return False, "FEN must be a non-empty string"
    
    fen = fen.strip()
    if not fen:
        return False, "FEN cannot be empty or whitespace only"
    
    try:
        # Try to create a board from the FEN
        board = chess.Board(fen)
        
        # Additional validation checks
        if not board.is_valid():
            return False, "Board position is not valid (e.g., kings in check, invalid piece placement)"
        
        logger.debug(f"FEN validation successful: {fen}")
        return True, None
        
    except ValueError as e:
        # chess.Board() raises ValueError for invalid FEN in newer versions
        error_msg = f"Invalid FEN format: {str(e)}"
        logger.warning(f"FEN validation failed: {error_msg}")
        return False, error_msg
    except Exception as e:
        error_msg = f"Unexpected error validating FEN: {str(e)}"
        logger.error(f"FEN validation error: {error_msg}")
        return False, error_msg

def validate_analysis_request(data: Dict[str, Any]) -> Tuple[bool, Optional[str], Optional[Dict[str, Any]]]:
    """
    Validate a request payload for the analysis endpoint.
    
    Args:
        data: Request JSON data
        
    Returns:
        Tuple of (is_valid, error_message, validated_data)
        - is_valid: True if request is valid, False otherwise
        - error_message: None if valid, error description if invalid
        - validated_data: Cleaned and validated request data
    """
    if not isinstance(data, dict):
        return False, "Request body must be a JSON object", None
    
    # Check for required 'fen' field
    if 'fen' not in data:
        return False, "Missing required field: 'fen'", None
    
    fen = data['fen']
    
    # Validate FEN string
    fen_valid, fen_error = validate_fen(fen)
    if not fen_valid:
        return False, f"Invalid FEN: {fen_error}", None
    
    # Validate optional 'time' field
    time_limit = data.get('time', 2.0)  # Default to 2 seconds

    try:
        time_limit = float(time_limit)
        if time_limit <= 0:
            return False, "Analysis time must be positive", None
        if time_limit > 30:  # Reasonable upper limit
            return False, "Analysis time cannot exceed 30 seconds", None
    except (ValueError, TypeError):
        return False, "Analysis time must be a valid number", None

    # Validate optional 'multiPv' field
    multi_pv = data.get('multiPv', 1)  # Default to 1 (single best move)

    try:
        # Check if it's already an integer or can be converted without loss
        if isinstance(multi_pv, float) and not multi_pv.is_integer():
            return False, "MultiPV must be a valid integer", None

        multi_pv = int(multi_pv)
        if multi_pv < 1:
            return False, "MultiPV must be at least 1", None
        if multi_pv > 10:  # Reasonable upper limit to prevent performance issues
            return False, "MultiPV cannot exceed 10", None
    except (ValueError, TypeError):
        return False, "MultiPV must be a valid integer", None
    
    # Return validated data
    validated_data = {
        'fen': fen.strip(),
        'time': time_limit,
        'multiPv': multi_pv
    }
    
    logger.debug(f"Request validation successful: {validated_data}")
    return True, None, validated_data

def format_error_response(message: str, status_code: int = 400) -> Dict[str, Any]:
    """
    Format a standardized error response.
    
    Args:
        message: Error message
        status_code: HTTP status code
        
    Returns:
        Error response dictionary
    """
    return {
        'error': message,
        'status': 'error',
        'code': status_code
    }

def format_success_response(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Format a standardized success response.
    
    Args:
        data: Response data
        
    Returns:
        Success response dictionary
    """
    return {
        'status': 'success',
        'data': data
    }

# Common FEN strings for testing
STARTING_POSITION_FEN = "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1"
SICILIAN_DEFENSE_FEN = "rnbqkbnr/pp1ppppp/8/2p5/4P3/8/PPPP1PPP/RNBQKBNR w KQkq c6 0 2"
QUEENS_GAMBIT_FEN = "rnbqkbnr/ppp1pppp/8/3p4/2PP4/8/PP2PPPP/RNBQKBNR b KQkq c3 0 2"
