

# **Technical Spike Research: Advanced Chess Suggestion System**

## **Executive Summary**

This document presents a comprehensive technical spike research analysis for the development of an advanced chess suggestion system. The system is conceived as an intelligent assistant designed to help new and intermediate players improve by providing real-time move suggestions and analysis, but only at specific, pedagogically valuable "important moments" within a game. This approach avoids creating a simple cheating tool and instead focuses on targeted, educational intervention.

The core technical challenge of this project lies in the robust and reliable acquisition of real-time game state data from a variety of heterogeneous sources, including a structured API, a complex and obfuscated web Document Object Model (DOM), and raw screen captures. Once acquired, this game state must be fed into a high-performance analysis engine capable of identifying these "important moments" with minimal latency.

### **Key Findings and Recommendations**

This research has yielded a series of key findings and architectural recommendations, which form the basis of a proposed phased development strategy:

* **Phase 1 (Lichess.org Integration):** The most stable and lowest-risk entry point for the project is integration with Lichess.org. Its public API is exceptionally comprehensive, well-documented, and provides dedicated endpoints for real-time event and game state streaming.1 The recommended implementation path involves utilizing the  
  GET /api/stream/event and GET /api/board/game/stream/{gameId} endpoints. Authentication can be handled initially with a user-generated Personal Access Token (PAT) for simplicity, with a later migration to OAuth 2.0 for a production-level application. The Python library berserk is recommended for its feature completeness and active maintenance.3  
* **Phase 2 (Chess.com Integration):** Integration with Chess.com is technically feasible but presents considerable technical and operational risks. As Chess.com does not offer a public API for real-time game play, the only viable method is a browser extension that scrapes game state information directly from the DOM. This approach is inherently fragile and susceptible to breaking whenever Chess.com updates its front-end code.5 Furthermore, it carries risks related to Terms of Service violations and potential triggering of anti-cheating mechanisms.7 The implementation would rely on the  
  MutationObserver API to detect changes to the move list in the DOM.  
* **Phase 3 (Computer Vision Fallback):** To address the limitations of the DOM-based approach and provide a universal solution, a computer vision (CV) fallback mechanism is proposed. This system would be a cross-platform desktop application, for which the **Electron.js** framework is recommended due to its mature ecosystem and JavaScript-based development environment.10 The CV pipeline would use  
  **OpenCV** for screen capture and board detection, followed by a custom-trained **Convolutional Neural Network (CNN)** for piece recognition. The CNN approach is strongly favored over template matching due to its superior accuracy and robustness against variations in piece styles and lighting.11  
* **"Important Moment" Analysis Engine:** The analytical core of the system will be a backend service that integrates with the **Stockfish** chess engine via the **Universal Chess Interface (UCI) protocol**.13 A backend architecture is decisively superior to an in-browser WebAssembly (WASM) implementation due to the significant performance limitations of WASM, which lacks access to full multi-threading and CPU-specific optimizations crucial for the NNUE evaluation function.15 The engine will use  
  **Multi-PV (Multi-Principal Variation)** analysis to identify tactical opportunities and critical moments by detecting large evaluation gaps between the best move and alternative lines.

### **Phased Rollout Synopsis**

A three-phase development approach is recommended to manage complexity and mitigate risk:

1. **Phase 1:** Focus on building the core backend analysis engine and integrating it with the stable Lichess.org API. This will validate the core product loop in a controlled environment.  
2. **Phase 2:** Develop the browser extension for Chess.com. This phase will tackle the challenges of DOM scraping and real-time move detection in a less controlled environment.  
3. **Phase 3:** Implement the computer vision-based desktop application. This phase is the most technically complex but provides a long-term, platform-agnostic solution that is resilient to changes in web interfaces.

This phased strategy allows for iterative development, with each phase building upon the backend infrastructure established in the first, while incrementally expanding the system's reach and robustness.

## **Part 1: Research on Game State Acquisition**

The foundational requirement for the chess suggestion system is the ability to acquire the current game state accurately and in real-time. This section explores three distinct technical approaches to achieve this, each targeting a different source platform and presenting a unique set of technical challenges and trade-offs.

### **1.1 API-Based Integration (Target: Lichess.org)**

Integration with Lichess.org via its official API represents the most robust, reliable, and developer-friendly method for game state acquisition. The platform's commitment to open-source principles has resulted in a powerful and well-documented API that is ideally suited for this project's requirements.

#### **1.1.1 Technical Overview of the Lichess API**

The Lichess API is a comprehensive, RESTful interface that provides programmatic access to nearly all platform features.1 For real-time applications, it employs a streaming model using Newline Delimited JSON (ND-JSON), where each line of the HTTP response is a self-contained JSON object. This is highly efficient for streaming continuous events without needing to parse a large, single JSON array.2

**Real-time Event and Game State Streaming**

Two primary endpoints are essential for the system's functionality:

1. **GET /api/stream/event**: This is the main entry point for monitoring the user's activity. After authenticating, the application can open a persistent connection to this endpoint. It streams key events such as challenge, gameStart, and gameFinish.1 When a  
   gameStart event is received, it will contain the gameId necessary to connect to the specific game stream.  
2. **GET /api/board/game/stream/{gameId}**: Once a game has started, the application uses the gameId to connect to this endpoint. This stream provides detailed, real-time updates for a single game.1 The stream begins with a  
   gameFull object, which contains the complete initial state of the game, including player information, time controls, and the starting Forsyth-Edwards Notation (FEN). Subsequently, for each move played, a gameState object is streamed, containing the latest moves in UCI notation, clock times, and status. This provides all the necessary information to maintain a perfect, real-time representation of the board state.

**Architectural Consideration: Board API vs. Bot API**

A crucial architectural decision is the choice between the Board API and the Bot API. The Lichess platform makes a clear distinction between applications designed to assist human players and fully autonomous bots.1

* **Bot API (/api/bot/\*)**: These endpoints are intended for accounts that have been permanently upgraded to a "BOT" status.19 These accounts are expected to be automated chess engines that play games without human intervention. They are subject to specific restrictions, such as being unable to participate in official tournaments.21 Using this API for a player-assistance tool would be a misuse of the API and could lead to account restrictions.  
* **Board API (/api/board/\*)**: This set of endpoints is designed for applications that act as a client for a human player, such as an interface for a physical DGT chessboard or, in this case, a training assistant.23

The proposed system is an assistant for a human, not an autonomous player. Therefore, it must authenticate as a regular user and utilize the general event stream (/api/stream/event) and the Board game stream (/api/board/game/stream/{gameId}). This ensures the application operates within the intended use case defined by Lichess and avoids subjecting the user's account to the limitations of a BOT account.

#### **1.1.2 Authentication Protocols**

The Lichess API supports two primary authentication methods, each suited for different use cases.1

* **Personal Access Tokens (PATs)**: This is the most straightforward method for authentication. A user can generate a token from their account settings page, selecting the specific permissions (scopes) the token should have, such as board:play or game:read.26 The application then includes this token in the  
  Authorization header of its API requests as a Bearer token (Authorization: Bearer {token}).1 PATs are long-lived and simple to use, making them ideal for initial development, personal scripts, and applications with a small number of tech-savvy users who can manage their own tokens securely.25  
* **OAuth 2.0 with PKCE**: For a public-facing application intended for a broad audience, OAuth 2.0 is the industry-standard and recommended method.1 It provides a secure authorization flow where users grant permission to the application by logging into Lichess directly, without ever exposing their credentials or manually handling tokens.26 The application redirects the user to Lichess, the user approves the request, and Lichess redirects back with an authorization code. The application then exchanges this code for an access token. This flow, specifically using the Proof Key for Code Exchange (PKCE) extension, is secure for public clients like web and mobile apps.1

**Recommendation**: For Phase 1 of development, using a **Personal Access Token** is sufficient and will accelerate initial implementation. However, the system architecture should be designed with a future migration to the **OAuth 2.0 flow** in mind for a production release. The OAuth 2.0 flow provides a more secure and seamless user experience, which is essential for building trust and scaling to a larger user base.25

#### **1.1.3 Data Formats and Protocols**

The Lichess API uses several standard chess data formats. The application must be capable of parsing and handling each of them.

* **Forsyth-Edwards Notation (FEN)**: FEN is a standard text notation that describes a particular board position in a single line of text.32 It captures all the information needed to restart a game from a specific point: piece placement, active color (whose turn it is), castling availability, and any possible en-passant target square.33 The FEN string is the primary data format that will be passed to the Stockfish engine for evaluation.  
* **Portable Game Notation (PGN)**: PGN is the standard for recording the moves of an entire chess game.34 While the Lichess API provides endpoints to export full games as PGN 35, for real-time analysis, the move-by-move updates from the game stream are more practical. PGN is more relevant for post-game analysis or for importing games.  
* **Universal Chess Interface (UCI) Notation**: UCI notation represents moves by their starting and ending squares (e.g., e2e4 for a pawn move, g1f3 for a knight move, e7e8q for a pawn promotion to a queen).38 This format is unambiguous and perfectly suited for programmatic communication between a client and a chess engine, which is why it is used in the Lichess API's puzzle and engine communication data.33 The application will need a robust chess logic library to convert between UCI and other formats as needed.

#### **1.1.4 Client Library Evaluation**

Several Python libraries exist to simplify interaction with the Lichess API.

* **berserk**: This library is the officially-endorsed Python client listed in the Lichess API documentation.3 It is actively maintained, feature-complete, and provides a clean, object-oriented interface that maps closely to the API endpoints (e.g.,  
  client.board.stream\_game\_state).3 It handles token-based authentication sessions and can be integrated with OAuth2 flows.41 Community discussions frequently recommend  
  berserk as the go-to choice for Python-based Lichess applications.4  
* **python-lichess (formerly lichesspy)**: This is another popular client library designed for ease of use and tight integration with the python-chess library.35 It provides convenient wrappers for API endpoints and can return data in formats directly usable by  
  python-chess (e.g., as a chess.Board object).42  
* **async\_lichess\_sdk**: This library offers an asynchronous interface to the Lichess API, built on asyncio and aiohttp.45 This could offer performance benefits in a highly concurrent environment, but for an application serving a single user at a time, the added complexity of an async-first architecture may not be necessary.

**Recommendation**: **berserk** is the recommended client library for this project. Its status as the officially-endorsed client, comprehensive feature set, active maintenance, and clear API mapping make it the most reliable and robust choice.3

#### **1.1.5 Risks and Mitigation**

* **API Rate Limiting**: The Lichess API enforces rate limits to ensure server stability. If the application makes too many requests in a short period, it will receive an HTTP 429 Too Many Requests status code. The API documentation specifies that upon receiving a 429 response, the client must wait for one full minute before making any further requests.2  
  * **Mitigation**: The application's network layer must include logic to handle the 429 response code gracefully. This involves implementing an exponential backoff or a simple one-minute wait strategy to respect the API limits and prevent being blocked.  
* **API and Data Format Changes**: As a living project, the Lichess API may undergo changes. Endpoints could be modified, or the structure of the JSON objects in the data streams could be altered.  
  * **Mitigation**: Using a well-maintained client library like berserk is the primary mitigation strategy. The library maintainers are likely to update the library in response to API changes, abstracting this complexity away from the application code. Additionally, the application should have robust data validation and error handling to manage unexpected data formats without crashing.  
* **Network Connectivity Issues**: The application relies on a persistent streaming connection to the Lichess servers. This connection can be interrupted by network instability on either the client or server side.  
  * **Mitigation**: The application must implement a resilient connection manager. This manager should be ableto detect a dropped connection (e.g., via a timeout or a TCP error), and automatically attempt to reconnect using a backoff strategy. When reconnecting to a game stream, it should be prepared to handle the initial gameFull object again to resynchronize its state.

### **1.2 DOM-Based Integration (Target: Chess.com)**

Unlike Lichess, Chess.com does not provide a public API for real-time game monitoring. Therefore, integrating with Chess.com requires a fundamentally different and more fragile approach: directly interacting with the website's Document Object Model (DOM) via a browser extension. This method is technically feasible but carries significant risks that must be carefully managed.

#### **1.2.1 Browser Extension Architecture**

The integration will be implemented as a browser extension, following modern standards to ensure compatibility and security.

* **Manifest V3**: The extension will be built using the Manifest V3 specification, which is the current standard for Chrome and other Chromium-based browsers. This architecture mandates the use of a background service worker instead of a persistent background page and enforces a more restrictive permissions model, enhancing security.  
* **Content Scripts**: The core of the extension's data-gathering logic will reside in a content script. This JavaScript file is injected directly into the context of the Chess.com game pages (e.g., pages matching the URL pattern https://www.chess.com/game/live/\*).46 The content script has direct access to the page's DOM, allowing it to read and parse the game state.  
* **Background Service Worker**: The service worker acts as the central coordinator for the extension. It will receive game state updates parsed by the content script and will be responsible for communicating this information to the backend analysis engine. This separation of concerns (DOM interaction in the content script, external communication in the service worker) is a best practice under Manifest V3.

#### **1.2.2 DOM Parsing for State Extraction**

The primary challenge of this approach is reliably extracting game information from a complex, dynamic, and potentially obfuscated web page.

* **The Challenge of Obfuscation and Minification**: Production web applications like Chess.com typically minify and obfuscate their front-end code to reduce file size and protect intellectual property.5 This means that HTML class names and IDs are often short, meaningless, and can change with each new deployment (e.g., from  
  move-list-container to \_a8f7c). Relying on static selectors is therefore extremely brittle.  
* **Methods for Locating Game Data**:  
  1. **Move List (PGN) Scraping**: The most promising method is to locate the DOM element that contains the game's move list. Many community-built extensions function by extracting the PGN from the game, often by programmatically clicking the "Share" button and capturing the resulting PGN data.21 This suggests that the PGN data is made available in a relatively stable and accessible format through this user-facing feature. The content script would need to identify the elements corresponding to the move list, parse the SAN (Standard Algebraic Notation) moves, and reconstruct the game history.  
  2. **Board State Reconstruction**: As a fallback, the script could parse the visual chessboard itself. The board is typically rendered as a collection of \<div\> elements, possibly within a custom element like \<chess-board\>.49 Each piece on a square is often represented by a  
     \<div\> with a specific class name indicating the piece type and color (e.g., wp for white pawn, bn for black knight).49 The script could iterate through the 64 square elements, identify the piece classes within each, and reconstruct a 2D array representation of the board. This is more complex and less complete than parsing the PGN, as it doesn't capture castling rights or en-passant status.

#### **1.2.3 Real-Time Move Detection**

To provide real-time suggestions, the extension cannot simply parse the state once. It must detect when a new move is made.

* **The MutationObserver API**: The standard and most efficient way to achieve this is with the MutationObserver JavaScript API.50 A  
  MutationObserver can be configured to watch a specific DOM element and its descendants for changes.  
* **Implementation Strategy**: The content script will first identify the DOM element that serves as the container for the move list. It will then create and attach a MutationObserver to this container. The observer will be configured to monitor for childList mutations, meaning it will trigger its callback function whenever a new child element (i.e., a new move) is added to the list.50 When the callback fires, the script will parse the newly added move, update its internal representation of the game state (e.g., using a library like  
  chess.js to apply the move and generate a new FEN), and send the updated FEN to the background service worker for analysis.  
* **Robustness Considerations**: The MutationObserver callback may be triggered by other, irrelevant DOM changes within the container. The callback logic must be robust enough to filter these out and only act on genuine move additions.51 It must also handle the possibility that the entire move list is re-rendered at once, rather than having a new move appended, which would require re-parsing the entire list.

#### **1.2.4 Risks and Challenges**

This approach is fraught with significant risks that must be acknowledged.

* **Extreme Fragility**: The reliance on the specific DOM structure of Chess.com is the greatest technical risk. Any front-end update, from a minor CSS class name change to a major UI overhaul, has a high probability of breaking the extension's parsing logic.6 This necessitates a commitment to continuous monitoring of the Chess.com site and frequent, reactive updates to the extension, which represents a significant ongoing maintenance cost.  
* **Terms of Service and Legal Risks**: Scraping content from a website without permission can be a violation of its Terms of Service. Chess.com has a history of data scraping incidents where third parties used their public API to collect user data, which was then leaked.8 While this project's client-side scraping is different, any unauthorized automated interaction with the platform could be viewed negatively. Under regulations like the GDPR, unauthorized scraping of personal data can be considered a data breach, carrying potential legal consequences.8 The platform could implement technical countermeasures or take legal action against services that violate its ToS.  
* **Anti-Cheating Detection**: Chess.com employs sophisticated anti-cheating systems that monitor for suspicious user behavior, including unusual mouse movements, rapid tab switching, and interaction with browser developer tools.7 An extension that programmatically reads from and interacts with the game interface, even with benign intent, runs the risk of being flagged by these systems. This could lead to warnings or account suspension for the user. The extension must be designed to be as passive and non-intrusive as possible to minimize this risk.

The confluence of these risks—technical fragility, ongoing maintenance burden, and potential ToS/legal issues—makes the DOM-based approach a high-risk endeavor. This risk profile strongly justifies the strategic importance of developing the computer vision-based fallback (detailed in the next section) as a more durable, platform-agnostic alternative for long-term viability.

### **1.3 Computer Vision-Based Fallback (General Purpose)**

To overcome the limitations of platform-specific integrations and provide a truly universal solution, a computer vision (CV)-based fallback system is proposed. This system would function as a standalone desktop application capable of reading the chessboard from the user's screen, regardless of the website or application being used. While the most complex to implement, this approach offers the greatest long-term resilience and flexibility.

#### **1.3.1 Desktop Application Frameworks**

The foundation of the CV system is a cross-platform desktop application that can perform screen capture and run the image processing pipeline.

* **Electron.js**: A mature and widely-used framework for building desktop applications using web technologies (JavaScript, HTML, CSS).10 It works by bundling a full Chromium browser and a Node.js runtime, which provides a consistent development and execution environment across Windows, macOS, and Linux.55 Its main advantages are its vast ecosystem of libraries (including those for screen capture and system interaction), a large community, and the ability to use a single language (JavaScript/TypeScript) for the entire application.56 The primary drawback is the larger application bundle size due to the inclusion of Chromium and Node.js.57  
* **Tauri**: A modern, lightweight alternative to Electron.57 Tauri applications leverage the operating system's native webview for the front end and use a Rust-based backend.55 This results in significantly smaller application sizes and lower memory consumption.58 However, this approach requires developers to be proficient in Rust for the backend logic, and reliance on different native webviews can lead to inconsistencies in UI rendering across platforms.58

**Recommendation**: **Electron.js** is the recommended framework for this project. Its maturity, extensive documentation, and the unified JavaScript/TypeScript development environment outweigh the benefits of Tauri's smaller bundle size. For a complex application involving screen capture, CV processing, and communication with a backend, the stability and rich library support of the Electron ecosystem provide a more pragmatic and faster path to a robust implementation.10

#### **1.3.2 Image Processing for Board Detection**

The first step in the CV pipeline is to capture a portion of the screen and precisely locate the 8x8 chessboard within it. This is a multi-stage process that can be implemented using the OpenCV library.

1. **Screen Capture**: The Electron application will use a library like screen\_capture\_lite or a similar Node.js module to capture an image of the user's screen or a selected window.62 The user will likely need to select the region of the screen containing the chessboard for the initial setup.  
2. **Preprocessing**: The captured image is first converted to grayscale to simplify processing. A Gaussian blur is then applied to reduce image noise, which can interfere with edge and line detection.  
3. **Board Localization**: The goal is to find the four corners of the chessboard's outer boundary.  
   * **Line Detection**: A common approach is to first use an edge detector, such as the Canny algorithm, to identify sharp changes in intensity. The Hough Line Transform (cv2.HoughLinesP) can then be applied to the resulting edge map to detect the horizontal and vertical lines that constitute the chessboard grid.63 By clustering these lines and finding their intersections, the grid structure can be determined.  
   * **Contour Detection**: An alternative method involves using cv2.findContours to identify all closed shapes in the image. The algorithm would then search for the largest square-like contour, which is likely to be the outer border of the chessboard.  
4. **Perspective Transformation**: Once the four outer corners of the board are identified, the image may still be skewed due to the viewing angle. To correct this, cv2.getPerspectiveTransform is used to compute a transformation matrix, which is then applied with cv2.warpPerspective. This process "unwarps" the image, producing a flat, perfectly square, top-down view of the chessboard, which is essential for accurate piece recognition.

It is important to note that OpenCV's built-in cv2.findChessboardCorners function is designed for camera calibration using high-contrast, empty checkerboard patterns and is generally not reliable for detecting the grid of a board with pieces on it.64 Therefore, a custom pipeline based on line or contour detection is required.

#### **1.3.3 Piece Recognition Methodologies**

After isolating a top-down image of the board, the next critical step is to identify the piece on each of the 64 squares.

* **Template Matching**: This technique involves creating a set of template images—one for each piece type and color (e.g., a canonical image of a white knight, a black pawn, etc.). The algorithm then slides each template over each of the 64 squares of the board image and uses a function like cv2.matchTemplate to calculate a similarity score.66 The template with the highest score above a certain threshold is chosen as the recognized piece.  
  * **Advantages**: Simple to implement and does not require model training.  
  * **Disadvantages**: This method is extremely brittle and not suitable for a general-purpose application. Its performance degrades severely with variations in piece design, scale, rotation, and lighting conditions.11 It would require an extensive and constantly updated library of templates for every chess website and application, which is impractical.69  
* **Machine Learning (Convolutional Neural Network \- CNN)**: A far more robust approach is to train a CNN for image classification. The problem is framed as classifying an image of a single square into one of 13 categories: wP, wN, wB, wR, wQ, wK, bP, bN, bB, bR, bQ, bK, and empty.  
  * **Advantages**: CNNs are highly resilient to variations in appearance. Once trained on a diverse dataset, a CNN can accurately recognize pieces across different visual styles, sizes, and lighting conditions, making it a truly universal solution. Projects utilizing this approach have reported accuracies exceeding 99%.12  
  * **Disadvantages**: This approach requires a substantial upfront investment in data collection, labeling, and model training.  
  * **Training Datasets**: Fortunately, several public datasets for chess piece recognition are available, which can significantly accelerate the process. Platforms like Kaggle and Roboflow host datasets containing thousands of labeled images of chess pieces and boards, captured from various angles and under different conditions.71 The  
    **Chess Recognition Dataset (ChessReD)** is particularly noteworthy, comprising 10,800 images from real-world scenarios with detailed annotations, making it an excellent resource for training a robust model.76

**Recommendation**: A **Convolutional Neural Network** is the unequivocally recommended approach for piece recognition. The brittleness of template matching makes it unviable for a production-grade, general-purpose tool. The investment in developing a CNN is a prerequisite for the success of this phase, as it is the only method that can deliver the required accuracy and adaptability.

#### **1.3.4 State Reconstruction to FEN**

The final step in the CV pipeline is to convert the classified board data into a standard FEN string that can be sent to the analysis engine.

* **Process**: The output of the CNN will be an 8x8 grid or array, where each cell contains the classification for that square (e.g., wP, bK, empty). A conversion function must be implemented to traverse this grid, typically from rank 8 down to rank 1 and from the a-file to the h-file for each rank.  
* **Algorithm**: The algorithm will iterate through the grid and build the FEN string piece by piece. It will translate the internal piece codes to the standard FEN characters (e.g., wP becomes P, bN becomes N, bK becomes k). It must also count consecutive empty squares and represent them with a number (e.g., four empty squares in a row becomes 4). A / character is used to separate the ranks.12 The  
  python-chess library also includes functions that can generate a FEN string from a board object, which can be used after populating the object from the CV output.78  
* **Limitations**: It is crucial to understand that this process can only reconstruct the first part of a FEN string—the piece placement. A single static image of the board does not contain information about the **active color** (whose turn it is), **castling rights**, the **en-passant target square**, or the **move counters**. To obtain this information, the system would either have to prompt the user to input it manually during setup or attempt to infer it by comparing board states between two consecutive screen captures. For an initial implementation, manual user input is the most reliable method.

## **Part 2: Research on "Important Moment" Detection Engine**

The core intelligence of the system resides in its ability to analyze a game state and determine when to intervene with a suggestion. This "Important Moment" Detection Engine relies on the computational power of a standard chess engine and a set of well-defined algorithms to interpret the engine's output in a pedagogically useful way.

### **2.1 Chess Engine Integration**

The foundation of the analysis engine is a seamless and efficient integration with a powerful chess engine like Stockfish. The standard for this communication is the Universal Chess Interface (UCI) protocol.

#### **2.1.1 The Universal Chess Interface (UCI) Protocol**

The UCI protocol is an open, text-based standard that governs communication between a chess engine and a graphical user interface (GUI) or any other controlling program.13 It operates over the standard input and standard output streams of the engine process, making it platform-independent and relatively simple to implement.14

**Core Communication Flow**: A typical interaction between the application (acting as the GUI) and the engine follows a well-defined sequence of commands 83:

1. **Startup and Initialization**: The application launches the engine's executable file. It then sends the command uci to the engine's standard input.  
2. **Engine Identification**: The engine responds by sending back its identification (id name Stockfish, id author...) and a list of all configurable options it supports (e.g., option name Hash type spin...). It concludes this handshake by sending uciok.13  
3. **Position Setup**: Before asking for an analysis, the application must describe the position to the engine. This is done with the position command. This command can take a FEN string (position fen rnbqkbnr/... w KQkq \- 0 1\) or specify a starting position and a sequence of moves in UCI notation (position startpos moves e2e4 e7e5 g1f3).84 This "stateless" design, where the GUI is responsible for maintaining the game state, is a central tenet of the UCI protocol.14  
4. **Initiating Analysis**: The application tells the engine to start thinking using the go command. This command can be appended with various parameters to control the search, such as depth \<d\> (search to a specific ply depth), movetime \<ms\> (search for a fixed amount of time), or infinite (search until a stop command is sent).84  
5. **Receiving Analysis Data**: While the engine is searching, it provides a continuous stream of info strings. These strings contain rich data about the search progress, including the current search depth, nodes per second (nps), the evaluation score (in cp for centipawns or mate for a forced mate), and the current pv (principal variation), which is the sequence of moves the engine considers best.84  
6. **Final Move**: Once the search concludes (or is stopped), the engine sends a final bestmove command, indicating its recommended move (e.g., bestmove e2e4 ponder e7e5).83

**Libraries for UCI Communication**: Implementing a UCI parser and process manager from scratch is unnecessary. The **python-chess** library provides a high-level and robust UCI wrapper (chess.uci) that handles the complexities of starting the engine process, sending commands, and parsing the info and bestmove responses into convenient Python objects.34 This library is the recommended tool for engine communication.

#### **2.1.2 Architectural Analysis: Backend vs. In-Browser (WASM)**

A critical architectural choice is where the chess engine will run: as a native process on a backend server or directly in the user's browser using WebAssembly (WASM).

* **Backend Server Process**: In this model, a native binary of Stockfish is executed on a server. The user's client application (web or desktop) sends FEN strings to this backend via an API, and the backend returns the analysis results.  
  * **Advantages**:  
    * **Maximum Performance**: This is the primary advantage. A native engine can utilize the full power of the server's hardware, including all CPU cores for parallel search, large amounts of RAM for the transposition table (hash), and specialized CPU instruction sets (like AVX2 or NEON) that dramatically accelerate the NNUE evaluation function.16  
    * **Full Feature Set**: A backend can support large endgame tablebases (e.g., Syzygy), which provide perfect play in positions with seven or fewer pieces, a feature that is impractical in a browser environment due to storage and memory constraints.15  
    * **Centralized Control**: Engine versions and configurations can be managed and updated centrally on the server.  
  * **Disadvantages**:  
    * **Operational Cost**: Requires hosting and maintaining server infrastructure.  
    * **Network Latency**: Communication between the client and the backend introduces network delay, which must be factored into the user experience.  
* **In-Browser (WebAssembly/WASM)**: Stockfish has been successfully compiled to WASM, allowing it to be downloaded and run directly within the user's web browser.86  
  * **Advantages**:  
    * **Zero Server Cost**: The computational load is entirely on the client, eliminating server costs for analysis.  
    * **No Network Latency**: Analysis is performed locally, resulting in instantaneous feedback once the engine is running.  
  * **Disadvantages**:  
    * **Significantly Reduced Performance**: This is the critical drawback. WASM runs in a sandboxed browser environment and cannot achieve the same performance as a native binary. It has limited access to multi-threading and cannot use the highly optimized, hand-written assembly and SIMD instructions that are key to Stockfish's speed.16 Performance can be  
      **30-50% slower** than a native build.15  
    * **Resource Constraints**: The engine is limited by the memory allocated by the browser tab, which restricts the size of the transposition table and prevents the use of tablebases.

**Recommendation**: A **backend server architecture** is strongly recommended. The core value proposition of the suggestion system is providing high-quality, deep analysis in a timely manner. The severe performance degradation of the WASM approach would compromise the quality and speed of the analysis, directly undermining the user's experience. While the backend model introduces operational costs and latency, these are manageable trade-offs for the immense performance gains and access to the full feature set of a native Stockfish engine. The convenience of WASM does not justify the sacrifice in analytical power for this specific application.

### **2.2 Tactical and Critical Analysis Algorithms**

The system's intelligence is not just in running a chess engine, but in interpreting its output to identify moments that are rich for learning. This requires specific algorithms designed to detect tactical opportunities, critical junctures, and user blunders.

#### **2.2.1 Leveraging Multi-PV Analysis**

Standard engine analysis provides the single best line of play (Principal Variation, or PV). However, to understand the tactical landscape of a position, it is often necessary to look deeper.

* **Multi-Principal Variation (Multi-PV)**: This is a standard feature in UCI-compliant engines, configured via the MultiPV option.13 Setting  
  MultiPV N instructs the engine to find and report the top N best moves and their corresponding variations.87  
* **Application for Tactical Detection**: The true value of Multi-PV for this system is its ability to uncover hidden tactics. In a single-PV search, an engine might quickly discard a move involving a sacrifice because its shallow evaluation is poor. The engine's search algorithm prunes this "bad" move and never explores it to a greater depth where its brilliance might be revealed.88 By setting  
  MultiPV to a value like 5, we force the engine to allocate search resources to not just the most obvious move, but to the top 5 candidate moves. This significantly increases the chance of discovering a complex tactical shot that might otherwise have been missed.90 A move that is initially ranked low but then shoots to the top of the list with a decisive evaluation after a deeper search is a hallmark of a non-obvious tactic.

#### **2.2.2 Algorithm Design for Opportunity and Blunder Detection**

By combining Multi-PV analysis with evaluation scores, we can design a set of algorithms to identify "important moments."

* **Tactic Finder / Critical Point Algorithm**: This algorithm runs *before* the user makes a move to determine if the current position is a critical moment.  
  1. **Configure Engine**: Set the engine to analyze with MultiPV 5 (a reasonable starting point).92  
  2. **Analyze Position**: Send the current FEN to the engine and run the analysis to a sufficient depth (e.g., depth 18-20) to ensure tactical lines are stable.  
  3. **Retrieve Evaluations**: From the info strings, extract the centipawn evaluations for the top N moves. Let these be E1​,E2​,…,EN​.  
  4. **Identify Critical Gap**: A tactical opportunity or critical point exists if the evaluation gap between the best move and the second-best move is substantial. The condition is: E1​−E2​≥Ttactic​, where Ttactic​ is a predefined threshold (e.g., 150 centipawns).  
  5. **Trigger Intervention**: If this condition is met, the system flags the position as an "important moment." It should pause and prompt the user with a message like, "This is an important moment. There is a strong move here, can you find it?"  
* **Blunder Detection Algorithm**: This algorithm runs *after* the user has made a move to provide feedback on their choice, especially if it was a mistake in a critical position.  
  1. **Store Pre-Move Analysis**: The system must have already run the "Critical Point" algorithm and stored the evaluation of the best move, Ebest​.  
  2. **Get User Move**: The system receives the move played by the user, Muser​.  
  3. **Evaluate User Move**: The system applies the user's move to the board and asks the engine for the evaluation of the *new* position, let's call this Enew​. Note that engine evaluations are from the perspective of the current player, so if it was White's turn, Enew​ is the evaluation after Black's best reply. To compare apples-to-apples, we need the evaluation of the position immediately after White's move. A simpler and more direct method is to compare the centipawn loss.  
  4. **Calculate Centipawn Loss (CPL)**: Let the evaluation of the position *before* the user's move be Einitial​. Let the evaluation of the position *after* the user's move be Efinal​. The centipawn loss is CPL=Einitial​−Efinal​ (adjusting for whose turn it is). A more direct method is to compare the evaluation of the best move (Ebest​) with the evaluation of the user's chosen move (Euser\_move​). The CPL is ∣Ebest​−Euser\_move​∣.  
  5. Detect Blunder: A blunder is detected if two conditions are met:  
     a. The centipawn loss exceeds a blunder threshold: CPL≥Tblunder​ (e.g., 200 centipawns).  
     b. The move results in a significant change in the game's expected outcome (e.g., moving from a winning state to a drawn or losing state).94  
  6. **Trigger Feedback**: If a blunder is detected, the system intervenes with feedback, such as, "You played {user move}, but {best move} was a much stronger option. Let's see why."

#### **2.2.3 Defining Analytical Thresholds**

The effectiveness of these algorithms hinges on well-defined and context-aware thresholds for what constitutes an inaccuracy, mistake, or blunder. Raw centipawn values alone are insufficient.

* **Centipawn (cp)**: The base unit of evaluation, where 100 centipawns is conventionally equivalent to the material advantage of one pawn.96 A positive score favors White; a negative score favors Black.  
* **Baseline Thresholds (Lichess Model)**: Lichess has established a widely-used and effective set of thresholds for move classification, which serves as an excellent starting point 99:  
  * **Inaccuracy**: A centipawn loss of **50-99 cp**.  
  * **Mistake**: A centipawn loss of **100-299 cp**.  
  * **Blunder**: A centipawn loss of **\>= 300 cp**.  
* **The Importance of Context**: A critical refinement, also employed by Lichess, is to contextualize the centipawn loss.95 A 200cp drop in a position where a player is already winning by a large margin (e.g., from \+10 to \+8) is not a true blunder because the outcome of the game is unlikely to change.94 A true blunder is a move that dramatically alters the win probability, such as turning a winning position into a drawn one, or a drawn position into a losing one.  
* **Proposed Operational Thresholds for the System**:  
  * **Tactical Opportunity / Critical Point**: A gap of **\>= 150 centipawns** between the evaluation of the best move and the second-best move. This value, equivalent to 1.5 pawns, represents a significant enough difference to indicate that one move is clearly superior and finding it is critical.92  
  * **Blunder (for post-move feedback)**: A centipawn loss of **\>= 200 centipawns**, **AND** the move must cross a "state" threshold. We can define game states as:  
    * Winning: ∣eval∣≥2.0  
    * Slight Advantage: 0.5≤∣eval∣\<2.0  
    * Equal: ∣eval∣\<0.5  
      A move is only flagged as a blunder if it causes a state change (e.g., from "Winning" to "Equal" or "Losing"). This prevents flagging moves in overwhelmingly won or lost positions, providing more meaningful feedback to the user.94

The decision to model the analysis engine on Lichess's discrete move classification system (Inaccuracy, Mistake, Blunder) rather than an aggregate "accuracy" percentage (as seen on Chess.com 102) is a deliberate one. The project's goal is to provide targeted, actionable feedback on specific moves. A single accuracy score is less informative for this purpose, as a player could have a high score overall but still make a single, game-losing blunder that the system should highlight. The Lichess model provides the granularity needed for effective, moment-by-moment pedagogical intervention.

## **Conclusion & Synthesis: A Phased Development Blueprint**

The research detailed in this document provides a clear and comprehensive technical foundation for building the advanced chess suggestion system. The analysis of game state acquisition methods and the design of the "Important Moment" engine lead to a logical, phased development blueprint. This approach is designed to manage technical risk, deliver value iteratively, and build a robust, multi-platform system over time.

* **Phase 1: Lichess Integration and Core Engine Development**  
  * **Objective**: To establish and validate the core product loop on the most stable and developer-friendly platform.  
  * **Architecture**: This phase will focus on creating the Python backend service. This service will use the berserk library to connect to the Lichess API, authenticating with a Personal Access Token for initial simplicity. It will listen to the gameStart event stream and then connect to the board/game/stream for real-time move updates. The backend will host a native Stockfish engine process, managed and controlled via the python-chess library's UCI wrapper. A minimal front-end client will be developed to receive and display the analysis results from the backend. This phase proves the viability of the analysis engine and the core suggestion logic.  
* **Phase 2: Chess.com Integration via Browser Extension**  
  * **Objective**: To extend the system's reach to the largest online chess platform, Chess.com.  
  * **Architecture**: This phase involves the development of a Manifest V3 browser extension for Chrome and other Chromium browsers. A content script injected into Chess.com game pages will use the MutationObserver API to monitor the DOM for new moves. When a move is detected, the updated game state (as a FEN string) will be sent to the same backend service developed in Phase 1\. This phase requires a significant engineering effort focused on creating resilient DOM parsing logic and establishing a maintenance process to handle frequent updates to the Chess.com website.  
* **Phase 3: Universal Computer Vision Fallback**  
  * **Objective**: To create a platform-agnostic solution that is resilient to web interface changes and can support any chess application.  
  * **Architecture**: This phase entails building a cross-platform desktop application using the Electron.js framework. The application will manage screen capture and execute an OpenCV-based computer vision pipeline to detect the chessboard and its squares. Piece recognition will be performed by a custom-trained Convolutional Neural Network (CNN), using a framework like TensorFlow.js or an ONNX runtime for inference within the Electron app. The reconstructed FEN string will be sent to the backend service from Phase 1\. This phase is the most technically demanding but ensures the long-term viability and universality of the product.

The following table provides a high-level summary of the recommended technology stack and architectural approach for each phase, serving as a quick-reference guide for implementation.

| Phase | Target Platform | Core Technology / Method | Key Libraries / APIs | Architectural Notes & Key Risks |
| :---- | :---- | :---- | :---- | :---- |
| **Phase 1** | Lichess.org | API-Based Integration | Lichess Board API (/api/board/game/stream), berserk (Python), python-chess (for UCI & logic) | Backend-centric architecture. Stockfish runs as a server process. Low risk, stable API. Initial auth via PAT. |
| **Phase 2** | Chess.com | DOM-Based Integration | Chrome Extension (Manifest V3), MutationObserver API, JavaScript | High risk of breakage due to website UI updates. Requires robust, non-static DOM selectors. Potential ToS/legal concerns. |
| **Phase 3** | General Purpose | Computer Vision | Electron.js, OpenCV, TensorFlow.js / ONNX Runtime | Most complex phase. Requires training a CNN model for piece recognition. State reconstruction from image is partial (lacks castling/en-passant rights). |

This phased blueprint provides a clear roadmap for development, allowing the project to proceed from a stable foundation towards progressively more complex and versatile implementations, ultimately realizing the vision of a universal, intelligent chess assistant.

## **Appendix**

### **A. Referenced APIs, Libraries, and Frameworks**

* **APIs**  
  * Lichess API: [https://lichess.org/api](https://lichess.org/api)  
  * MutationObserver API:([https://developer.mozilla.org/en-US/docs/Web/API/MutationObserver](https://developer.mozilla.org/en-US/docs/Web/API/MutationObserver))  
  * Universal Chess Interface (UCI): [http://wbec-ridderkerk.nl/html/UCIProtocol.html](http://wbec-ridderkerk.nl/html/UCIProtocol.html)  
* **Libraries & Frameworks**  
  * async\_lichess\_sdk (Python): [https://pypi.org/project/async-lichess-sdk/](https://pypi.org/project/async-lichess-sdk/)  
  * berserk (Python): [https://github.com/lichess-org/berserk](https://github.com/lichess-org/berserk)  
  * chess.js (JavaScript): [https://github.com/jhlywa/chess.js](https://github.com/jhlywa/chess.js)  
  * Electron.js: [https://www.electronjs.org/](https://www.electronjs.org/)  
  * OpenCV: [https://opencv.org/](https://opencv.org/)  
  * python-chess (Python): [https://python-chess.readthedocs.io/](https://python-chess.readthedocs.io/)  
  * python-lichess / lichesspy (Python): [https://pypi.org/project/lichesspy/](https://pypi.org/project/lichesspy/)  
  * Stockfish Chess Engine: [https://stockfishchess.org/](https://stockfishchess.org/)  
  * Tauri: [https://tauri.app/](https://tauri.app/)  
  * TensorFlow.js: [https://www.tensorflow.org/js](https://www.tensorflow.org/js)  
  * ONNX Runtime: [https://onnxruntime.ai/](https://onnxruntime.ai/)

### **B. Key Technical Articles and Research Papers**

* Anderson, R. (2016, March 4). *A basic Chrome extension: analyze your chess.com games on lichess.org*. ZeroSharp Blog. 104  
* Chess.com Forum Discussion. (2014). *Chess.com Developer Program*. 105  
* Chess.com Forum Discussion. (2022). *Mistake versus inaccuracy*. 106  
* Chess Programming Wiki. (n.d.). *Universal Chess Interface (UCI)*. 14  
* Jakim, P. (n.d.). *Chess-Tactic-Finder*. GitHub Repository. 92  
* Lichess.org Forum Discussion. (2020). *Learn from your mistakes*. 95  
* Meyer-Kahlen, S., & Huber, R. (n.d.). *The UCI Protocol*. Shredder Chess. 82  
* Solon, N. (2022, October 12). *Centipawns Suck*. 107  
* Stack Exchange Discussion. (2019). *Average centipawn loss*. 101  
* Stack Exchange Discussion. (2023). *Is there a way to use stockfish to see tactics that aren't necessarily great?*. 90  
* Wikipedia. (n.d.). *Universal Chess Interface*. 80

#### **Nguồn trích dẫn**

1. Lichess.org API reference, truy cập vào tháng 6 23, 2025, [https://lichess.org/api](https://lichess.org/api)  
2. api/doc/specs/lichess-api.yaml at master · lichess-org/api \- GitHub, truy cập vào tháng 6 23, 2025, [https://github.com/lichess-org/api/blob/master/doc/specs/lichess-api.yaml](https://github.com/lichess-org/api/blob/master/doc/specs/lichess-api.yaml)  
3. lichess-org/berserk: Python client for the lichess API \- GitHub, truy cập vào tháng 6 23, 2025, [https://github.com/lichess-org/berserk](https://github.com/lichess-org/berserk)  
4. Examples/help with using the Lichess API?, truy cập vào tháng 6 23, 2025, [https://lichess.org/forum/general-chess-discussion/exampleshelp-with-using-the-lichess-api](https://lichess.org/forum/general-chess-discussion/exampleshelp-with-using-the-lichess-api)  
5. I made Chess.com Extension which will blow your mind., truy cập vào tháng 6 23, 2025, [https://www.chess.com/forum/view/fun-with-chess/i-made-chess-com-extension-which-will-blow-your-mind-96713261](https://www.chess.com/forum/view/fun-with-chess/i-made-chess-com-extension-which-will-blow-your-mind-96713261)  
6. Chess.com Analysis at Lichess \- Chrome Web Store, truy cập vào tháng 6 23, 2025, [https://chromewebstore.google.com/detail/chesscom-analysis-at-lich/bhjlkimpkkgkmfjlcfngmakenalgleap](https://chromewebstore.google.com/detail/chesscom-analysis-at-lich/bhjlkimpkkgkmfjlcfngmakenalgleap)  
7. How does Chess. Com detects Engine even if we play Human+engine moves (balanced)?, truy cập vào tháng 6 23, 2025, [https://www.reddit.com/r/chess/comments/17vw9gm/how\_does\_chess\_com\_detects\_engine\_even\_if\_we\_play/](https://www.reddit.com/r/chess/comments/17vw9gm/how_does_chess_com_detects_engine_even_if_we_play/)  
8. Chess.com data leak?\! \- Reddit, truy cập vào tháng 6 23, 2025, [https://www.reddit.com/r/chess/comments/17rv9lp/chesscom\_data\_leak/](https://www.reddit.com/r/chess/comments/17rv9lp/chesscom_data_leak/)  
9. Chess.com Data Breach: What Happened and How to Prevent It \- StrongDM, truy cập vào tháng 6 23, 2025, [https://www.strongdm.com/what-is/chess-com-data-breach](https://www.strongdm.com/what-is/chess-com-data-breach)  
10. How to Build a Cross-Platform Desktop App for Windows, Mac, and Linux \- Sigma Solve, truy cập vào tháng 6 23, 2025, [https://www.sigmasolve.com/blog/cross-platform-desktop-app-development/](https://www.sigmasolve.com/blog/cross-platform-desktop-app-development/)  
11. Chess Piece Detection \- Digital Commons @ Cal Poly, truy cập vào tháng 6 23, 2025, [https://digitalcommons.calpoly.edu/cgi/viewcontent.cgi?article=1617\&context=eesp](https://digitalcommons.calpoly.edu/cgi/viewcontent.cgi?article=1617&context=eesp)  
12. Chess FEN Generator \- Kaggle, truy cập vào tháng 6 23, 2025, [https://www.kaggle.com/code/koryakinp/chess-fen-generator](https://www.kaggle.com/code/koryakinp/chess-fen-generator)  
13. UCI & Commands \- Stockfish Docs, truy cập vào tháng 6 23, 2025, [https://official-stockfish.github.io/docs/stockfish-wiki/UCI-&-Commands.html](https://official-stockfish.github.io/docs/stockfish-wiki/UCI-&-Commands.html)  
14. UCI \- Chessprogramming wiki, truy cập vào tháng 6 23, 2025, [https://www.chessprogramming.org/UCI](https://www.chessprogramming.org/UCI)  
15. How Much Weaker are Browser Engines Compared To Local Engines, and Why? : r/chess, truy cập vào tháng 6 23, 2025, [https://www.reddit.com/r/chess/comments/1acyvfg/how\_much\_weaker\_are\_browser\_engines\_compared\_to/](https://www.reddit.com/r/chess/comments/1acyvfg/how_much_weaker_are_browser_engines_compared_to/)  
16. Lichess uses Stockfish 14+ NNUE compiled with WASM, so it should be essentially \- Hacker News, truy cập vào tháng 6 23, 2025, [https://news.ycombinator.com/item?id=29572790](https://news.ycombinator.com/item?id=29572790)  
17. Lichess API \- PublicAPI, truy cập vào tháng 6 23, 2025, [https://publicapi.dev/lichess-api](https://publicapi.dev/lichess-api)  
18. Lichess API for playing a game \- Reddit, truy cập vào tháng 6 23, 2025, [https://www.reddit.com/r/lichess/comments/qk9kpe/lichess\_api\_for\_playing\_a\_game/](https://www.reddit.com/r/lichess/comments/qk9kpe/lichess_api_for_playing_a_game/)  
19. How to make my lichess bot play moves?, truy cập vào tháng 6 23, 2025, [https://lichess.org/forum/general-chess-discussion/how-to-make-my-lichess-bot-play-moves](https://lichess.org/forum/general-chess-discussion/how-to-make-my-lichess-bot-play-moves)  
20. Lichess Bots, truy cập vào tháng 6 23, 2025, [https://lichess.org/team/lichess-bots](https://lichess.org/team/lichess-bots)  
21. Playing as a lichess Bot- easy way, truy cập vào tháng 6 23, 2025, [https://lichess.org/forum/off-topic-discussion/playing-as-a-lichess-bot-easy-way](https://lichess.org/forum/off-topic-discussion/playing-as-a-lichess-bot-easy-way)  
22. lichess-bot-devs/lichess-bot: A bridge between Lichess ... \- GitHub, truy cập vào tháng 6 23, 2025, [https://github.com/lichess-bot-devs/lichess-bot](https://github.com/lichess-bot-devs/lichess-bot)  
23. Using the Lichess API as a non bot \- Reddit, truy cập vào tháng 6 23, 2025, [https://www.reddit.com/r/lichess/comments/cx0lvu/using\_the\_lichess\_api\_as\_a\_non\_bot/](https://www.reddit.com/r/lichess/comments/cx0lvu/using_the_lichess_api_as_a_non_bot/)  
24. API to launch game remotely \- Lichess, truy cập vào tháng 6 23, 2025, [https://lichess.org/forum/lichess-feedback/api-to-launch-game-remotely](https://lichess.org/forum/lichess-feedback/api-to-launch-game-remotely)  
25. api/example/README.md at master · lichess-org/api \- GitHub, truy cập vào tháng 6 23, 2025, [https://github.com/lichess-org/api/blob/master/example/README.md](https://github.com/lichess-org/api/blob/master/example/README.md)  
26. Connecting With Lichess API, truy cập vào tháng 6 23, 2025, [https://lichess.org/forum/lichess-feedback/connecting-with-lichess-api](https://lichess.org/forum/lichess-feedback/connecting-with-lichess-api)  
27. How to create a token on Lichess | Tutorial \- YouTube, truy cập vào tháng 6 23, 2025, [https://www.youtube.com/watch?v=4kmGo-51i2g](https://www.youtube.com/watch?v=4kmGo-51i2g)  
28. Programming an Application that access Lichess API : r/chess \- Reddit, truy cập vào tháng 6 23, 2025, [https://www.reddit.com/r/chess/comments/cwxtso/programming\_an\_application\_that\_access\_lichess\_api/](https://www.reddit.com/r/chess/comments/cwxtso/programming_an_application_that_access_lichess_api/)  
29. Personal API access tokens \- Lichess.org, truy cập vào tháng 6 23, 2025, [https://lichess.org/forum/general-chess-discussion/personal-api-access-tokens](https://lichess.org/forum/general-chess-discussion/personal-api-access-tokens)  
30. Lichess \- Arctic, truy cập vào tháng 6 23, 2025, [https://arcticjs.dev/providers/lichess](https://arcticjs.dev/providers/lichess)  
31. Personal Access Tokens vs OAuth \- Information Security Stack Exchange, truy cập vào tháng 6 23, 2025, [https://security.stackexchange.com/questions/266707/personal-access-tokens-vs-oauth](https://security.stackexchange.com/questions/266707/personal-access-tokens-vs-oauth)  
32. API Reference · Chess.jl, truy cập vào tháng 6 23, 2025, [https://romstad.github.io/Chess.jl/dev/api/](https://romstad.github.io/Chess.jl/dev/api/)  
33. lichess.org open database, truy cập vào tháng 6 23, 2025, [https://database.lichess.org/](https://database.lichess.org/)  
34. python-chess: a chess library for Python — python-chess 1.11.2 documentation, truy cập vào tháng 6 23, 2025, [https://python-chess.readthedocs.io/](https://python-chess.readthedocs.io/)  
35. Python API client.games.export \- Lichess, truy cập vào tháng 6 23, 2025, [https://lichess.org/forum/general-chess-discussion/python-api-clientgamesexport](https://lichess.org/forum/general-chess-discussion/python-api-clientgamesexport)  
36. API Methods — python-lichess documentation \- Read the Docs, truy cập vào tháng 6 23, 2025, [https://python-lichess.readthedocs.io/en/latest/api.html](https://python-lichess.readthedocs.io/en/latest/api.html)  
37. is it possible to pass a PGN line to Lichess via the URL (not fen) \- Reddit, truy cập vào tháng 6 23, 2025, [https://www.reddit.com/r/lichess/comments/kollls/is\_it\_possible\_to\_pass\_a\_pgn\_line\_to\_lichess\_via/](https://www.reddit.com/r/lichess/comments/kollls/is_it_possible_to_pass_a_pgn_line_to_lichess_via/)  
38. Why the solution notation in Lichess API is different from the pgn (standard) notation, truy cập vào tháng 6 23, 2025, [https://lichess.org/forum/general-chess-discussion/why-the-solution-notation-in-lichess-api-is-different-from-the-pgn-standard-notation-](https://lichess.org/forum/general-chess-discussion/why-the-solution-notation-in-lichess-api-is-different-from-the-pgn-standard-notation-)  
39. Extract Chess Data from Lichess API with Python \- YouTube, truy cập vào tháng 6 23, 2025, [https://www.youtube.com/watch?v=OnCQ3J6ZKL4](https://www.youtube.com/watch?v=OnCQ3J6ZKL4)  
40. rhgrant10/berserk: Python client for the lichess API \- GitHub, truy cập vào tháng 6 23, 2025, [https://github.com/rhgrant10/berserk](https://github.com/rhgrant10/berserk)  
41. berserk \- PyPI, truy cập vào tháng 6 23, 2025, [https://pypi.org/project/berserk/0.2.1/](https://pypi.org/project/berserk/0.2.1/)  
42. python-lichess: a client for the lichess.org API — python-lichess documentation, truy cập vào tháng 6 23, 2025, [https://python-lichess.readthedocs.io/](https://python-lichess.readthedocs.io/)  
43. eskopp/lichesspy: A simple Python API wrapper to use the Lichess API. \- GitHub, truy cập vào tháng 6 23, 2025, [https://github.com/eskopp/lichesspy](https://github.com/eskopp/lichesspy)  
44. python-lichess Documentation, truy cập vào tháng 6 23, 2025, [https://python-lichess.readthedocs.io/\_/downloads/en/latest/pdf/](https://python-lichess.readthedocs.io/_/downloads/en/latest/pdf/)  
45. lichess\_python\_SDK | Async Python Lichess SDK \- GitHub Pages, truy cập vào tháng 6 23, 2025, [https://amasend.github.io/lichess\_python\_SDK/html/](https://amasend.github.io/lichess_python_SDK/html/)  
46. Free Chess Analysis \- Chrome Web Store, truy cập vào tháng 6 23, 2025, [https://chromewebstore.google.com/detail/free-chess-analysis/fkakfklkhmjkkehkjchemdgeoldekdml](https://chromewebstore.google.com/detail/free-chess-analysis/fkakfklkhmjkkehkjchemdgeoldekdml)  
47. I created a free chess.com analysis extension for google chrome : r/lichess \- Reddit, truy cập vào tháng 6 23, 2025, [https://www.reddit.com/r/lichess/comments/11f1u6f/i\_created\_a\_free\_chesscom\_analysis\_extension\_for/](https://www.reddit.com/r/lichess/comments/11f1u6f/i_created_a_free_chesscom_analysis_extension_for/)  
48. ZeroSharp/Chess.com\_Analysis\_Chrome\_extension: A ... \- GitHub, truy cập vào tháng 6 23, 2025, [https://github.com/ZeroSharp/Chess.com\_Analysis\_Chrome\_extension](https://github.com/ZeroSharp/Chess.com_Analysis_Chrome_extension)  
49. Chess.com no click move \- GitHub Gist, truy cập vào tháng 6 23, 2025, [https://gist.github.com/trurl-master/27749b6dff46cec9af0a845fe6916c8e](https://gist.github.com/trurl-master/27749b6dff46cec9af0a845fe6916c8e)  
50. I am trying to detect changes on a particular element using the MutationObserver but it is not working. What am I doing wrong? \- Stack Overflow, truy cập vào tháng 6 23, 2025, [https://stackoverflow.com/questions/72351142/i-am-trying-to-detect-changes-on-a-particular-element-using-the-mutationobserver](https://stackoverflow.com/questions/72351142/i-am-trying-to-detect-changes-on-a-particular-element-using-the-mutationobserver)  
51. JavaScript, MutationObserver misses mutations \- dom \- Stack Overflow, truy cập vào tháng 6 23, 2025, [https://stackoverflow.com/questions/65691170/javascript-mutationobserver-misses-mutations](https://stackoverflow.com/questions/65691170/javascript-mutationobserver-misses-mutations)  
52. What happened in the Chess.com data breach? \- Twingate, truy cập vào tháng 6 23, 2025, [https://www.twingate.com/blog/tips/chess-com-data-breach](https://www.twingate.com/blog/tips/chess-com-data-breach)  
53. Undetectable by Design: The Code Behind the Cheaters \- Chess.com, truy cập vào tháng 6 23, 2025, [https://www.chess.com/blog/Jordi641/undetectable-by-design-the-code-behind-the-cheaters](https://www.chess.com/blog/Jordi641/undetectable-by-design-the-code-behind-the-cheaters)  
54. What approach can i take to developing a Desktop application using Web Technologies, truy cập vào tháng 6 23, 2025, [https://stackoverflow.com/questions/2351330/what-approach-can-i-take-to-developing-a-desktop-application-using-web-technolog](https://stackoverflow.com/questions/2351330/what-approach-can-i-take-to-developing-a-desktop-application-using-web-technolog)  
55. Tauri vs Electron: A 2025 Comparison for Desktop Development | Codeology, truy cập vào tháng 6 23, 2025, [https://codeology.co.nz/articles/tauri-vs-electron-2025-desktop-development.html](https://codeology.co.nz/articles/tauri-vs-electron-2025-desktop-development.html)  
56. 4 Best frameworks for cross-platform desktop app development \- Scythe Studio, truy cập vào tháng 6 23, 2025, [https://scythe-studio.com/en/blog/4-best-frameworks-for-cross-platform-desktop-app-development](https://scythe-studio.com/en/blog/4-best-frameworks-for-cross-platform-desktop-app-development)  
57. Tauri vs. Electron: performance, bundle size, and the real trade-offs \- Hopp, truy cập vào tháng 6 23, 2025, [https://gethopp.app/blog/tauri-vs-electron](https://gethopp.app/blog/tauri-vs-electron)  
58. Tauri VS. Electron \- Real world application, truy cập vào tháng 6 23, 2025, [https://www.levminer.com/blog/tauri-vs-electron](https://www.levminer.com/blog/tauri-vs-electron)  
59. Tauri vs Electron ⚔️ Which One Should You Use in 2025? \- YouTube, truy cập vào tháng 6 23, 2025, [https://www.youtube.com/watch?v=nJ7xVcpnCdo](https://www.youtube.com/watch?v=nJ7xVcpnCdo)  
60. Tauri vs. Electron Benchmark: \~58% Less Memory, \~96% Smaller Bundle – Our Findings and Why We Chose Tauri : r/programming \- Reddit, truy cập vào tháng 6 23, 2025, [https://www.reddit.com/r/programming/comments/1jwjw7b/tauri\_vs\_electron\_benchmark\_58\_less\_memory\_96/](https://www.reddit.com/r/programming/comments/1jwjw7b/tauri_vs_electron_benchmark_58_less_memory_96/)  
61. Tauri vs Electron vs Swift for WebRTC screen sharing app? : r/webdev \- Reddit, truy cập vào tháng 6 23, 2025, [https://www.reddit.com/r/webdev/comments/1k9q3pr/tauri\_vs\_electron\_vs\_swift\_for\_webrtc\_screen/](https://www.reddit.com/r/webdev/comments/1k9q3pr/tauri_vs_electron_vs_swift_for_webrtc_screen/)  
62. smasherprog/screen\_capture\_lite: cross platform screen/window capturing library \- GitHub, truy cập vào tháng 6 23, 2025, [https://github.com/smasherprog/screen\_capture\_lite](https://github.com/smasherprog/screen_capture_lite)  
63. object-detection-opencv/chessboard-grid-detection.ipynb at master \- GitHub, truy cập vào tháng 6 23, 2025, [https://github.com/neemiasbsilva/object-detection-opencv/blob/master/chessboard-grid-detection.ipynb](https://github.com/neemiasbsilva/object-detection-opencv/blob/master/chessboard-grid-detection.ipynb)  
64. Find Patterns in a Chessboard Using OpenCV Python \- Tutorialspoint, truy cập vào tháng 6 23, 2025, [https://www.tutorialspoint.com/how-to-find-patterns-in-a-chessboard-using-opencv-python](https://www.tutorialspoint.com/how-to-find-patterns-in-a-chessboard-using-opencv-python)  
65. How do I get openCV to detect this chess board I made? \- Stack Overflow, truy cập vào tháng 6 23, 2025, [https://stackoverflow.com/questions/75378264/how-do-i-get-opencv-to-detect-this-chess-board-i-made](https://stackoverflow.com/questions/75378264/how-do-i-get-opencv-to-detect-this-chess-board-i-made)  
66. arashyeganeh/Chess-Piece-Object-Detection-using-OpenCV \- GitHub, truy cập vào tháng 6 23, 2025, [https://github.com/arashyeganeh/Chess-Piece-Object-Detection-using-OpenCV](https://github.com/arashyeganeh/Chess-Piece-Object-Detection-using-OpenCV)  
67. Template matching using OpenCV in Python \- GeeksforGeeks, truy cập vào tháng 6 23, 2025, [https://www.geeksforgeeks.org/python/template-matching-using-opencv-in-python/](https://www.geeksforgeeks.org/python/template-matching-using-opencv-in-python/)  
68. More precise Template Matching with OpenCV : r/learnpython \- Reddit, truy cập vào tháng 6 23, 2025, [https://www.reddit.com/r/learnpython/comments/12qtbug/more\_precise\_template\_matching\_with\_opencv/](https://www.reddit.com/r/learnpython/comments/12qtbug/more_precise_template_matching_with_opencv/)  
69. python \- Chess piece detection On chessboard Opencv \- Stack Overflow, truy cập vào tháng 6 23, 2025, [https://stackoverflow.com/questions/75281938/chess-piece-detection-on-chessboard-opencv](https://stackoverflow.com/questions/75281938/chess-piece-detection-on-chessboard-opencv)  
70. How to template match a simple 2D shape in OpenCV? \- Stack Overflow, truy cập vào tháng 6 23, 2025, [https://stackoverflow.com/questions/61779288/how-to-template-match-a-simple-2d-shape-in-opencv](https://stackoverflow.com/questions/61779288/how-to-template-match-a-simple-2d-shape-in-opencv)  
71. Chess Pieces Dataset \- Kaggle, truy cập vào tháng 6 23, 2025, [https://www.kaggle.com/datasets/ninadaithal/chess-pieces-dataset](https://www.kaggle.com/datasets/ninadaithal/chess-pieces-dataset)  
72. Chess Pieces Object Detection Dataset, truy cập vào tháng 6 23, 2025, [https://public.roboflow.com/object-detection/chess-full](https://public.roboflow.com/object-detection/chess-full)  
73. Chess Pieces Detection Images Dataset \- Kaggle, truy cập vào tháng 6 23, 2025, [https://www.kaggle.com/datasets/anshulmehtakaggl/chess-pieces-detection-images-dataset](https://www.kaggle.com/datasets/anshulmehtakaggl/chess-pieces-detection-images-dataset)  
74. Chess Piece Detection FYP Computer Vision Project \- Roboflow Universe, truy cập vào tháng 6 23, 2025, [https://universe.roboflow.com/fyp-pbsgo/chess-piece-detection-fyp](https://universe.roboflow.com/fyp-pbsgo/chess-piece-detection-fyp)  
75. LeventSoykan/Chess\_Piece\_Image\_Classification\_With\_CNN: Project for detection of chess piece type from images \- GitHub, truy cập vào tháng 6 23, 2025, [https://github.com/LeventSoykan/Chess\_Piece\_Image\_Classification\_With\_CNN](https://github.com/LeventSoykan/Chess_Piece_Image_Classification_With_CNN)  
76. Chess Recognition Dataset (ChessReD) \- 4TU Data Repository, truy cập vào tháng 6 23, 2025, [https://data.4tu.nl/datasets/99b5c721-280b-450b-b058-b2900b69a90f/2](https://data.4tu.nl/datasets/99b5c721-280b-450b-b058-b2900b69a90f/2)  
77. How to convert FEN ID onto a Chess Board? \- python \- Stack Overflow, truy cập vào tháng 6 23, 2025, [https://stackoverflow.com/questions/66451525/how-to-convert-fen-id-onto-a-chess-board](https://stackoverflow.com/questions/66451525/how-to-convert-fen-id-onto-a-chess-board)  
78. Core — python-chess 1.11.2 documentation, truy cập vào tháng 6 23, 2025, [https://python-chess.readthedocs.io/en/latest/core.html](https://python-chess.readthedocs.io/en/latest/core.html)  
79. Python libraries to generate FEN from moves : r/chessprogramming \- Reddit, truy cập vào tháng 6 23, 2025, [https://www.reddit.com/r/chessprogramming/comments/17m0e0l/python\_libraries\_to\_generate\_fen\_from\_moves/](https://www.reddit.com/r/chessprogramming/comments/17m0e0l/python_libraries_to_generate_fen_from_moves/)  
80. Universal Chess Interface \- Wikipedia, truy cập vào tháng 6 23, 2025, [https://en.wikipedia.org/wiki/Universal\_Chess\_Interface](https://en.wikipedia.org/wiki/Universal_Chess_Interface)  
81. UCI Protocol \- Shredder Chess, truy cập vào tháng 6 23, 2025, [https://www.shredderchess.com/chess-features/uci-universal-chess-interface.html](https://www.shredderchess.com/chess-features/uci-universal-chess-interface.html)  
82. UCI (=universal chess interface), truy cập vào tháng 6 23, 2025, [http://page.mi.fu-berlin.de/block/uci.htm](http://page.mi.fu-berlin.de/block/uci.htm)  
83. Intro \- UCI Docs \- Obsidian Publish, truy cập vào tháng 6 23, 2025, [https://publish.obsidian.md/modern-uci-doc/UCI+Docs/Intro](https://publish.obsidian.md/modern-uci-doc/UCI+Docs/Intro)  
84. UCI engine communication — python-chess 0.25.0 documentation, truy cập vào tháng 6 23, 2025, [https://python-chess.readthedocs.io/en/v0.25.0/uci.html](https://python-chess.readthedocs.io/en/v0.25.0/uci.html)  
85. WebAssembly port of the chess variant engine Fairy-Stockfish with NNUE support \- GitHub, truy cập vào tháng 6 23, 2025, [https://github.com/fairy-stockfish/fairy-stockfish.wasm](https://github.com/fairy-stockfish/fairy-stockfish.wasm)  
86. Stockfish (chess) \- Wikipedia, truy cập vào tháng 6 23, 2025, [https://en.wikipedia.org/wiki/Stockfish\_(chess)](https://en.wikipedia.org/wiki/Stockfish_\(chess\))  
87. Analysis Window \- Scid vs. PC, truy cập vào tháng 6 23, 2025, [https://scidvspc.sourceforge.net/doc/Analysis.htm](https://scidvspc.sourceforge.net/doc/Analysis.htm)  
88. Chess engine \- Chess Forums \- Chess.com, truy cập vào tháng 6 23, 2025, [https://www.chess.com/forum/view/game-analysis/chess-engine2](https://www.chess.com/forum/view/game-analysis/chess-engine2)  
89. confusion regarding game analysis with engine \- Chess Forums, truy cập vào tháng 6 23, 2025, [https://www.chess.com/forum/view/general/confusion-regarding-game-analysis-with-engine](https://www.chess.com/forum/view/general/confusion-regarding-game-analysis-with-engine)  
90. Is there a way to use Stockfish to see tactics that aren't necessarily great?, truy cập vào tháng 6 23, 2025, [https://chess.stackexchange.com/questions/41759/is-there-a-way-to-use-stockfish-to-see-tactics-that-arent-necessarily-great](https://chess.stackexchange.com/questions/41759/is-there-a-way-to-use-stockfish-to-see-tactics-that-arent-necessarily-great)  
91. StockFish : best move not found due to MultiPV ? \- TalkChess.com, truy cập vào tháng 6 23, 2025, [https://talkchess.com/viewtopic.php?t=67917](https://talkchess.com/viewtopic.php?t=67917)  
92. JakimPL/Chess-Tactic-Finder: A tool for finding chess puzzles out of your games. \- GitHub, truy cập vào tháng 6 23, 2025, [https://github.com/JakimPL/Chess-Tactic-Finder](https://github.com/JakimPL/Chess-Tactic-Finder)  
93. Multi PV Mode \- Chess Forums, truy cập vào tháng 6 23, 2025, [https://www.chess.com/forum/view/general/multi-pv-mode](https://www.chess.com/forum/view/general/multi-pv-mode)  
94. chess-blunders/README.md at main \- GitHub, truy cập vào tháng 6 23, 2025, [https://github.com/Antiochian/chess-blunders/blob/main/README.md](https://github.com/Antiochian/chess-blunders/blob/main/README.md)  
95. I'm beginning to get a big confused as to what really is a "blunder". \- Lichess, truy cập vào tháng 6 23, 2025, [https://lichess.org/forum/general-chess-discussion/im-beginning-to-get-a-big-confused-as-to-what-really-is-a-blunder](https://lichess.org/forum/general-chess-discussion/im-beginning-to-get-a-big-confused-as-to-what-really-is-a-blunder)  
96. Can someone explain what average centipawn loss is to me? Having a hard time understanding when I look it up on the internet. \- Reddit, truy cập vào tháng 6 23, 2025, [https://www.reddit.com/r/chessbeginners/comments/x0v3oo/can\_someone\_explain\_what\_average\_centipawn\_loss/](https://www.reddit.com/r/chessbeginners/comments/x0v3oo/can_someone_explain_what_average_centipawn_loss/)  
97. average centipawn loss \- Chess Forums, truy cập vào tháng 6 23, 2025, [https://www.chess.com/forum/view/chess-openings/average-centipawn-loss](https://www.chess.com/forum/view/chess-openings/average-centipawn-loss)  
98. What is centipawns? • page 1/2 • General Chess Discussion ..., truy cập vào tháng 6 23, 2025, [https://lichess.org/forum/general-chess-discussion/what-is-centipawns\#:\~:text=Centipawn%20loss%20is%20how%20many,if%20you%20care%20to%20calculate.](https://lichess.org/forum/general-chess-discussion/what-is-centipawns#:~:text=Centipawn%20loss%20is%20how%20many,if%20you%20care%20to%20calculate.)  
99. What is Average Centipawn Loss? How is Average Centipawn Loss Calculated? I got Centipawn Loss of 3\! \- YouTube, truy cập vào tháng 6 23, 2025, [https://www.youtube.com/watch?v=H9jAjRiWn7Q](https://www.youtube.com/watch?v=H9jAjRiWn7Q)  
100. With LiChess, what is the difference between an inaccuracy, a mistake, and a blunder? : r/chess \- Reddit, truy cập vào tháng 6 23, 2025, [https://www.reddit.com/r/chess/comments/3q4mvz/with\_lichess\_what\_is\_the\_difference\_between\_an/](https://www.reddit.com/r/chess/comments/3q4mvz/with_lichess_what_is_the_difference_between_an/)  
101. Average centipawn loss \- Chess Stack Exchange, truy cập vào tháng 6 23, 2025, [https://chess.stackexchange.com/questions/26469/average-centipawn-loss](https://chess.stackexchange.com/questions/26469/average-centipawn-loss)  
102. How is accuracy in Analysis determined? | Chess.com Help Center, truy cập vào tháng 6 23, 2025, [https://support.chess.com/en/articles/8708970-how-is-accuracy-in-analysis-determined](https://support.chess.com/en/articles/8708970-how-is-accuracy-in-analysis-determined)  
103. How does chess.com calculate accuracy? \- Chess Forums, truy cập vào tháng 6 23, 2025, [https://www.chess.com/forum/view/game-analysis/how-does-chess-com-calculate-accuracy](https://www.chess.com/forum/view/game-analysis/how-does-chess-com-calculate-accuracy)  
104. A basic Chrome extension \- analyze your chess.com games on ..., truy cập vào tháng 6 23, 2025, [http://blog.zerosharp.com/a-basic-chrome-extension-analyze-your-chess-dot-com-games-on-lichess-dot-org/](http://blog.zerosharp.com/a-basic-chrome-extension-analyze-your-chess-dot-com-games-on-lichess-dot-org/)  
105. Chess.com Developer Program (EC) \- Forums, truy cập vào tháng 6 23, 2025, [https://www.chess.com/forum/view/community/chesscom-developer-program-ec](https://www.chess.com/forum/view/community/chesscom-developer-program-ec)  
106. Mistake versus inaccuracy \- Chess Forums, truy cập vào tháng 6 23, 2025, [https://www.chess.com/forum/view/for-beginners/mistake-versus-inaccuracy](https://www.chess.com/forum/view/for-beginners/mistake-versus-inaccuracy)  
107. Centipawn Loss per opening \- Chess Stack Exchange, truy cập vào tháng 6 23, 2025, [https://chess.stackexchange.com/questions/40519/centipawn-loss-per-opening](https://chess.stackexchange.com/questions/40519/centipawn-loss-per-opening)