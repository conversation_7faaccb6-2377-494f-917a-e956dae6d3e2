"""
Guardian Chess Tutor Backend Application

Flask-based REST API for chess position analysis using Stockfish engine.
"""

import logging
import os
from flask import Flask, jsonify, request
from .config import Config
from .uci_manager import UCIManager, UCIManagerError
from .lichess_manager import <PERSON><PERSON><PERSON>ana<PERSON>, LichessManagerError
from .utils import validate_analysis_request, format_error_response, format_success_response

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def create_app(config_class=Config):
    """Application factory pattern for creating Flask app."""
    app = Flask(__name__)
    app.config.from_object(config_class)
    
    # Initialize UCI Manager
    uci_manager = None
    try:
        uci_manager = UCIManager(app.config['STOCKFISH_PATH'])
        logger.info("UCI Manager initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize UCI Manager: {e}")

    # Initialize Lichess Manager
    lichess_manager = None
    try:
        lichess_pat = app.config.get('LICHESS_PAT')
        if lichess_pat:
            lichess_manager = LichessManager(lichess_pat)
            logger.info("Lichess Manager initialized successfully")

            # Set UCI manager for direct analysis
            if uci_manager:
                lichess_manager.set_uci_manager(uci_manager)

            # Start event streaming if authenticated
            if lichess_manager.is_authenticated():
                def game_start_handler(game_id: str):
                    """Handle new game start events."""
                    logger.info(f"🎯 Guardian detected new game: {game_id}")
                    # Real-time analysis is now handled automatically by the game stream

                success = lichess_manager.start_event_stream(game_start_handler)
                if success:
                    logger.info("🔴 LIVE: Lichess event stream started - Guardian is watching for games!")
                else:
                    logger.error("Failed to start Lichess event stream")
            else:
                logger.warning("Lichess Manager not authenticated - event streaming disabled")
        else:
            logger.info("No Lichess PAT configured - Lichess features disabled")
    except Exception as e:
        logger.error(f"Failed to initialize Lichess Manager: {e}")

    # Store managers in app context
    app.uci_manager = uci_manager
    app.lichess_manager = lichess_manager
    
    @app.route('/health', methods=['GET'])
    def health_check():
        """Health check endpoint."""
        status = {
            "status": "ok",
            "service": "Guardian Chess Tutor Backend",
            "version": "0.1.0"
        }
        
        # Check UCI Manager status
        if app.uci_manager:
            try:
                engine_info = app.uci_manager.get_engine_info()
                status["engine"] = {
                    "status": "connected",
                    "info": engine_info
                }
            except Exception as e:
                status["engine"] = {
                    "status": "error",
                    "error": str(e)
                }
        else:
            status["engine"] = {
                "status": "not_initialized",
                "error": "UCI Manager failed to initialize"
            }
        
        return jsonify(status)

    @app.route('/analyze', methods=['POST'])
    def analyze_position():
        """
        Analyze a chess position and return the best move.

        Expected JSON payload:
        {
            "fen": "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1",
            "time": 2.0,     // optional, analysis time in seconds
            "multiPv": 3     // optional, number of principal variations (default: 1)
        }
        """
        try:
            # Get JSON data from request
            if not request.is_json:
                return jsonify(format_error_response("Request must be JSON")), 400

            data = request.get_json()
            if not data:
                return jsonify(format_error_response("Request body cannot be empty")), 400

            # Validate request data
            is_valid, error_msg, validated_data = validate_analysis_request(data)
            if not is_valid:
                return jsonify(format_error_response(error_msg)), 400

            # Check if UCI manager is available
            if not app.uci_manager or not app.uci_manager.is_engine_ready():
                return jsonify(format_error_response(
                    "Chess engine is not available. Please check server configuration."
                )), 503

            # Perform analysis
            try:
                analysis_result = app.uci_manager.analyze_position(
                    validated_data['fen'],
                    validated_data['time'],
                    validated_data['multiPv']
                )

                logger.info(f"Analysis completed for FEN: {validated_data['fen']}")
                return jsonify(format_success_response(analysis_result))

            except UCIManagerError as e:
                logger.error(f"UCI analysis error: {e}")
                return jsonify(format_error_response(f"Analysis failed: {str(e)}")), 500

        except Exception as e:
            logger.error(f"Unexpected error in analyze endpoint: {e}")
            return jsonify(format_error_response("Internal server error")), 500

    @app.route('/lichess/account', methods=['GET'])
    def lichess_account():
        """
        Get Lichess account information for the authenticated user.

        Returns user account details from Lichess API.
        """
        try:
            # Check if Lichess manager is available
            if not app.lichess_manager:
                return jsonify(format_error_response(
                    "Lichess integration not configured. Please set LICHESS_PAT environment variable."
                )), 503

            # Check if authenticated
            if not app.lichess_manager.is_authenticated():
                return jsonify(format_error_response(
                    "Not authenticated with Lichess. Please check your PAT token."
                )), 401

            # Get account information
            try:
                account_info = app.lichess_manager.get_account_info()
                user_status = app.lichess_manager.get_user_status()

                response_data = {
                    'account': account_info,
                    'status': user_status
                }

                logger.info(f"Retrieved account info for user: {account_info.get('username', 'Unknown')}")
                return jsonify(format_success_response(response_data))

            except LichessManagerError as e:
                logger.error(f"Lichess API error: {e}")
                return jsonify(format_error_response(f"Lichess API error: {str(e)}")), 500

        except Exception as e:
            logger.error(f"Unexpected error in lichess account endpoint: {e}")
            return jsonify(format_error_response("Internal server error")), 500

    @app.route('/lichess/stream/status', methods=['GET'])
    def lichess_stream_status():
        """
        Get the status of the Lichess event stream.

        Returns information about whether the event stream is running and connected.
        """
        try:
            # Check if Lichess manager is available
            if not app.lichess_manager:
                return jsonify(format_error_response(
                    "Lichess integration not configured. Please set LICHESS_PAT environment variable."
                )), 503

            # Get stream status
            stream_status = app.lichess_manager.get_event_stream_status()

            response_data = {
                'stream': stream_status,
                'manager': {
                    'authenticated': app.lichess_manager.is_authenticated(),
                    'username': app.lichess_manager.account_info.get('username') if app.lichess_manager.account_info else None
                }
            }

            logger.debug("Retrieved Lichess stream status")
            return jsonify(format_success_response(response_data))

        except Exception as e:
            logger.error(f"Unexpected error in lichess stream status endpoint: {e}")
            return jsonify(format_error_response("Internal server error")), 500

    @app.errorhandler(404)
    def not_found(error):
        """Handle 404 errors."""
        return jsonify({"error": "Endpoint not found"}), 404
    
    @app.errorhandler(500)
    def internal_error(error):
        """Handle 500 errors."""
        logger.error(f"Internal server error: {error}")
        return jsonify({"error": "Internal server error"}), 500
    
    return app

def main():
    """Main entry point for running the application."""
    app = create_app()
    
    # Get configuration from environment
    host = os.getenv('FLASK_HOST', '127.0.0.1')
    port = int(os.getenv('FLASK_PORT', 5000))
    debug = os.getenv('FLASK_ENV') == 'development'
    
    logger.info(f"Starting Guardian Chess Tutor Backend on {host}:{port}")
    app.run(host=host, port=port, debug=debug)

if __name__ == '__main__':
    main()
