"""
Main entry point for the Guardian Chess Tutor Desktop Client.

This module initializes the PySide6 application and launches the main window.
"""

import sys
import logging
from typing import Optional
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import Qt
from .config import ClientConfig
from .ui.main_window import MainWindow


def setup_logging() -> None:
    """Configure logging for the application."""
    logging.basicConfig(
        level=getattr(logging, ClientConfig.LOG_LEVEL.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )


def create_application() -> QApplication:
    """
    Create and configure the QApplication.
    
    Returns:
        Configured QApplication instance
    """
    app = QApplication(sys.argv)
    
    # Set application properties
    app.setApplicationName("Guardian Chess Tutor")
    app.setApplicationVersion("0.1.0")
    app.setOrganizationName("Guardian Team")
    
    # Enable high DPI scaling (Qt 6 compatible)
    app.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
    
    return app


def main() -> int:
    """
    Main entry point for the application.
    
    Returns:
        Exit code (0 for success, non-zero for error)
    """
    # Setup logging first
    setup_logging()
    logger = logging.getLogger(__name__)
    
    logger.info("Starting Guardian Chess Tutor Desktop Client")
    
    try:
        # Create the application
        app = create_application()
        
        # Create and show the main window
        main_window = MainWindow()
        main_window.show()
        
        logger.info("Application started successfully")
        
        # Run the application event loop
        return app.exec()
        
    except Exception as e:
        logger.error(f"Failed to start application: {e}")
        return 1


if __name__ == '__main__':
    sys.exit(main())
