"""
<PERSON><PERSON> Brain Module for Guardian Chess Tutor Backend.

This module contains the intelligent pedagogical algorithms that form the core
of the Guardian Chess Tutor's teaching capabilities. It analyzes chess positions
and identifies critical moments, tactical opportunities, and learning points.
"""

import logging
from typing import Dict, Any, Optional, List
from enum import Enum

try:
    from .config import Config
except ImportError:
    try:
        from config import Config
    except ImportError:
        # Fallback for testing
        class Config:
            TACTIC_THRESHOLD = 150
            BLUNDER_THRESHOLD = 200
            EQUAL_STATE_THRESHOLD = 0.5
            WINNING_STATE_THRESHOLD = 2.0

class GameState(Enum):
    """Enumeration of possible game states based on evaluation."""
    LOSING = "losing"
    EQUAL = "equal"
    WINNING = "winning"

logger = logging.getLogger(__name__)

class TutorBrainError(Exception):
    """Custom exception for Tutor Brain errors."""
    pass

class TutorBrain:
    """
    The intelligent core of the Guardian Chess Tutor.
    
    This class contains algorithms for:
    - Critical moment detection
    - Tactical opportunity identification
    - Position evaluation analysis
    - Learning point extraction
    """
    
    def __init__(self, tactic_threshold: int = None, blunder_threshold: int = None,
                 equal_state_threshold: float = None, winning_state_threshold: float = None):
        """
        Initialize the Tutor Brain.

        Args:
            tactic_threshold: Centipawn threshold for critical moment detection.
                            Defaults to Config.TACTIC_THRESHOLD if not provided.
            blunder_threshold: Centipawn threshold for blunder detection.
                             Defaults to Config.BLUNDER_THRESHOLD if not provided.
            equal_state_threshold: Pawn threshold for equal game state.
                                 Defaults to Config.EQUAL_STATE_THRESHOLD if not provided.
            winning_state_threshold: Pawn threshold for winning game state.
                                   Defaults to Config.WINNING_STATE_THRESHOLD if not provided.
        """
        self.tactic_threshold = tactic_threshold or Config.TACTIC_THRESHOLD
        self.blunder_threshold = blunder_threshold or Config.BLUNDER_THRESHOLD
        self.equal_state_threshold = equal_state_threshold or Config.EQUAL_STATE_THRESHOLD
        self.winning_state_threshold = winning_state_threshold or Config.WINNING_STATE_THRESHOLD

        logger.info(f"Tutor Brain initialized with tactic threshold: {self.tactic_threshold} centipawns, "
                   f"blunder threshold: {self.blunder_threshold} centipawns, "
                   f"equal state: ±{self.equal_state_threshold} pawns, "
                   f"winning state: ±{self.winning_state_threshold} pawns")
    
    def is_critical_moment(self, analysis_data: Dict[str, Any]) -> bool:
        """
        Determine if a position contains a critical moment or tactical opportunity.
        
        A critical moment is defined as a position where there is a significant
        evaluation gap between the best move and the second-best move, indicating
        that one move is clearly superior and missing it would be a significant error.
        
        Args:
            analysis_data: Analysis data from the chess engine, containing:
                          - analysis: List of move variations with evaluations
                          - evaluation: Overall position evaluation
        
        Returns:
            True if this is a critical moment, False otherwise
        """
        try:
            # Validate input data
            if not analysis_data or not isinstance(analysis_data, dict):
                logger.debug("Invalid analysis data: not a dictionary")
                return False
            
            analysis_list = analysis_data.get('analysis', [])
            if not analysis_list or len(analysis_list) < 2:
                logger.debug("Insufficient analysis variations for critical moment detection")
                return False
            
            # Extract evaluations for best and second-best moves
            best_move_data = analysis_list[0]
            second_best_data = analysis_list[1]
            
            best_eval = self._extract_centipawn_value(best_move_data.get('evaluation'))
            second_eval = self._extract_centipawn_value(second_best_data.get('evaluation'))
            
            if best_eval is None or second_eval is None:
                logger.debug("Could not extract centipawn values from evaluations")
                return False
            
            # Calculate evaluation gap
            # The gap should always be positive (best move advantage)
            evaluation_gap = abs(best_eval - second_eval)
            
            # Determine if this is a critical moment
            is_critical = evaluation_gap >= self.tactic_threshold
            
            if is_critical:
                best_move = best_move_data.get('move', 'Unknown')
                second_move = second_best_data.get('move', 'Unknown')
                logger.debug(f"Critical moment detected: {best_move} ({best_eval}cp) vs {second_move} ({second_eval}cp), gap: {evaluation_gap}cp")
            else:
                logger.debug(f"Position is quiet: evaluation gap {evaluation_gap}cp < threshold {self.tactic_threshold}cp")
            
            return is_critical
            
        except Exception as e:
            logger.error(f"Error in critical moment detection: {e}")
            return False
    
    def _extract_centipawn_value(self, evaluation: Optional[Dict[str, Any]]) -> Optional[int]:
        """
        Extract centipawn value from an evaluation dictionary.
        
        Args:
            evaluation: Evaluation dictionary with type, value, and description
        
        Returns:
            Centipawn value as integer, or None if not available
        """
        if not evaluation or not isinstance(evaluation, dict):
            return None
        
        eval_type = evaluation.get('type')
        eval_value = evaluation.get('value')
        
        if eval_type == 'centipawns' and eval_value is not None:
            try:
                return int(eval_value)
            except (ValueError, TypeError):
                logger.warning(f"Could not convert centipawn value to int: {eval_value}")
                return None
        elif eval_type == 'mate':
            # For mate scores, convert to a large centipawn equivalent
            # Positive mate = very large positive value, negative mate = very large negative value
            if eval_value is not None:
                try:
                    mate_moves = int(eval_value)
                    # Convert mate in N to centipawn equivalent
                    # Mate in 1 = 10000cp, mate in 2 = 9000cp, etc.
                    if mate_moves > 0:
                        return max(10000 - (mate_moves - 1) * 1000, 5000)
                    else:
                        return min(-10000 - (abs(mate_moves) - 1) * 1000, -5000)
                except (ValueError, TypeError):
                    logger.warning(f"Could not convert mate value to int: {eval_value}")
                    return None
        
        logger.debug(f"Could not extract centipawn value from evaluation: {evaluation}")
        return None
    
    def analyze_critical_moment(self, analysis_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Perform detailed analysis of a critical moment.
        
        Args:
            analysis_data: Analysis data from the chess engine
        
        Returns:
            Dictionary containing critical moment analysis, or None if not critical
        """
        if not self.is_critical_moment(analysis_data):
            return None
        
        try:
            analysis_list = analysis_data.get('analysis', [])
            best_move_data = analysis_list[0]
            second_best_data = analysis_list[1]
            
            best_eval = self._extract_centipawn_value(best_move_data.get('evaluation'))
            second_eval = self._extract_centipawn_value(second_best_data.get('evaluation'))
            
            evaluation_gap = abs(best_eval - second_eval)
            
            # Determine the type of critical moment
            moment_type = self._classify_critical_moment(evaluation_gap, best_move_data, second_best_data)
            
            return {
                'is_critical': True,
                'type': moment_type,
                'evaluation_gap': evaluation_gap,
                'threshold': self.tactic_threshold,
                'best_move': {
                    'move': best_move_data.get('move'),
                    'evaluation': best_eval,
                    'description': best_move_data.get('evaluation', {}).get('description', 'N/A')
                },
                'second_best': {
                    'move': second_best_data.get('move'),
                    'evaluation': second_eval,
                    'description': second_best_data.get('evaluation', {}).get('description', 'N/A')
                },
                'advice': self._generate_advice(moment_type, evaluation_gap)
            }
            
        except Exception as e:
            logger.error(f"Error in critical moment analysis: {e}")
            return None
    
    def _classify_critical_moment(self, gap: int, best_data: Dict, second_data: Dict) -> str:
        """
        Classify the type of critical moment based on evaluation gap and move characteristics.
        
        Args:
            gap: Evaluation gap in centipawns
            best_data: Best move data
            second_data: Second best move data
        
        Returns:
            String describing the type of critical moment
        """
        if gap >= 500:
            return "major_tactical_opportunity"
        elif gap >= 300:
            return "significant_advantage"
        elif gap >= 150:
            return "tactical_opportunity"
        else:
            return "positional_advantage"
    
    def _generate_advice(self, moment_type: str, gap: int) -> str:
        """
        Generate educational advice for a critical moment.
        
        Args:
            moment_type: Type of critical moment
            gap: Evaluation gap in centipawns
        
        Returns:
            Educational advice string
        """
        advice_map = {
            "major_tactical_opportunity": f"This is a major tactical opportunity! The best move is worth {gap/100:.1f} pawns more than alternatives.",
            "significant_advantage": f"There's a significant advantage available worth {gap/100:.1f} pawns. Look for tactical motifs!",
            "tactical_opportunity": f"A tactical opportunity exists worth {gap/100:.1f} pawns. Consider all candidate moves carefully.",
            "positional_advantage": f"The best move provides a {gap/100:.1f} pawn advantage. Focus on the position's key features."
        }
        
        return advice_map.get(moment_type, f"This position offers a {gap/100:.1f} pawn advantage with the best move.")

    def analyze_user_move(self, pre_move_analysis: Dict[str, Any], user_move_uci: str) -> Dict[str, Any]:
        """
        Analyze a user's move to detect blunders using Centipawn Loss (CPL) calculation.

        This method compares the move the user played against the best available move
        from the pre-move analysis to calculate the centipawn loss and determine if
        the move was a blunder.

        Args:
            pre_move_analysis: Analysis data from before the user's move was played
            user_move_uci: The move the user played in UCI format (e.g., 'e2e4')

        Returns:
            Dictionary containing blunder analysis:
            - is_blunder: Boolean indicating if this was a blunder
            - centipawn_loss: CPL value if it's a blunder
            - best_move: The best move that was available
            - user_move_eval: Evaluation of the user's move
            - best_move_eval: Evaluation of the best move
            - severity: Classification of the blunder severity
        """
        try:
            # Validate input data
            if not pre_move_analysis or not isinstance(pre_move_analysis, dict):
                logger.debug("Invalid pre-move analysis data")
                return {"is_blunder": False, "error": "Invalid analysis data"}

            if not user_move_uci or not isinstance(user_move_uci, str):
                logger.debug("Invalid user move UCI")
                return {"is_blunder": False, "error": "Invalid move format"}

            analysis_list = pre_move_analysis.get('analysis', [])
            if not analysis_list:
                logger.debug("No analysis variations available")
                return {"is_blunder": False, "error": "No analysis available"}

            # Get the best move evaluation
            best_move_data = analysis_list[0]
            best_move_uci = best_move_data.get('move')
            best_move_eval = self._extract_centipawn_value(best_move_data.get('evaluation'))

            if best_move_eval is None:
                logger.debug("Could not extract best move evaluation")
                return {"is_blunder": False, "error": "Invalid best move evaluation"}

            # Find the user's move in the analysis
            user_move_eval = None
            user_move_found = False

            for variation in analysis_list:
                if variation.get('move') == user_move_uci:
                    user_move_eval = self._extract_centipawn_value(variation.get('evaluation'))
                    user_move_found = True
                    break

            # If user's move not found in top variations, treat as significantly worse
            if not user_move_found or user_move_eval is None:
                logger.debug(f"User move {user_move_uci} not found in top variations")
                # Estimate evaluation as worse than the last analyzed move
                if len(analysis_list) > 1:
                    last_move_eval = self._extract_centipawn_value(analysis_list[-1].get('evaluation'))
                    if last_move_eval is not None:
                        # Make it 100cp worse than the worst analyzed move
                        user_move_eval = last_move_eval - 100
                    else:
                        user_move_eval = best_move_eval - 300  # Default penalty
                else:
                    user_move_eval = best_move_eval - 300  # Default penalty for unanalyzed moves

            # Calculate Centipawn Loss (CPL)
            # CPL = Best Move Evaluation - User Move Evaluation
            # For positions where we're winning, this should be positive for blunders
            centipawn_loss = best_move_eval - user_move_eval

            # Determine game states before and after the move
            state_before = self._classify_game_state(best_move_eval)
            state_after = self._classify_game_state(user_move_eval)

            # Check for negative state change
            has_negative_state_change = self._is_negative_state_change(state_before, state_after)

            # Determine if this is a blunder (requires both CPL threshold AND negative state change)
            meets_cpl_threshold = centipawn_loss >= self.blunder_threshold
            is_critical_blunder = meets_cpl_threshold and has_negative_state_change

            # For backward compatibility, also flag as blunder if CPL is very high (500+)
            is_major_blunder = centipawn_loss >= 500
            is_blunder = is_critical_blunder or is_major_blunder

            # Classify blunder severity
            severity = self._classify_blunder_severity(centipawn_loss) if is_blunder else None

            result = {
                "is_blunder": is_blunder,
                "is_critical_blunder": is_critical_blunder,
                "centipawn_loss": centipawn_loss,
                "best_move": best_move_uci,
                "user_move": user_move_uci,
                "user_move_eval": user_move_eval,
                "best_move_eval": best_move_eval,
                "user_move_found": user_move_found,
                "threshold": self.blunder_threshold,
                "state_before": state_before.value,
                "state_after": state_after.value,
                "has_negative_state_change": has_negative_state_change,
                "meets_cpl_threshold": meets_cpl_threshold
            }

            if is_blunder:
                result["severity"] = severity
                result["advice"] = self._generate_contextual_advice(
                    severity, centipawn_loss, best_move_uci,
                    state_before, state_after, is_critical_blunder
                )

            return result

        except Exception as e:
            logger.error(f"Error in user move analysis: {e}")
            return {"is_blunder": False, "error": str(e)}

    def _classify_blunder_severity(self, centipawn_loss: int) -> str:
        """
        Classify the severity of a blunder based on centipawn loss.

        Args:
            centipawn_loss: The centipawn loss value

        Returns:
            String describing the blunder severity
        """
        if centipawn_loss >= 500:
            return "major_blunder"
        elif centipawn_loss >= 300:
            return "blunder"
        elif centipawn_loss >= 200:
            return "mistake"
        else:
            return "inaccuracy"

    def _generate_blunder_advice(self, severity: str, cpl: int, best_move: str) -> str:
        """
        Generate educational advice for a blunder.

        Args:
            severity: Severity classification of the blunder
            cpl: Centipawn loss value
            best_move: The best move that was available

        Returns:
            Educational advice string
        """
        advice_map = {
            "major_blunder": f"This was a major blunder! You lost {cpl/100:.1f} pawns. The best move was {best_move}.",
            "blunder": f"This was a blunder that cost {cpl/100:.1f} pawns. Consider {best_move} instead.",
            "mistake": f"This mistake cost {cpl/100:.1f} pawns. {best_move} was stronger.",
            "inaccuracy": f"A small inaccuracy worth {cpl/100:.1f} pawns. {best_move} was more accurate."
        }

        return advice_map.get(severity, f"This move cost {cpl/100:.1f} pawns. {best_move} was better.")

    def _generate_contextual_advice(self, severity: str, cpl: int, best_move: str,
                                  state_before: GameState, state_after: GameState,
                                  is_critical: bool) -> str:
        """
        Generate contextual educational advice for a blunder based on game state changes.

        Args:
            severity: Severity classification of the blunder
            cpl: Centipawn loss value
            best_move: The best move that was available
            state_before: Game state before the move
            state_after: Game state after the move
            is_critical: Whether this is a critical blunder (affects game outcome)

        Returns:
            Contextual educational advice string
        """
        if is_critical:
            # Critical blunder that changes game outcome
            state_change_desc = f"{state_before.value} → {state_after.value}"

            if state_before == GameState.WINNING and state_after == GameState.LOSING:
                return f"CRITICAL BLUNDER! Turned a winning position into a losing one ({state_change_desc}). CPL: {cpl} ({cpl/100:.1f} pawns). The best move was {best_move}."
            elif state_before == GameState.WINNING and state_after == GameState.EQUAL:
                return f"CRITICAL BLUNDER! Threw away a winning advantage ({state_change_desc}). CPL: {cpl} ({cpl/100:.1f} pawns). {best_move} maintained the win."
            elif state_before == GameState.EQUAL and state_after == GameState.LOSING:
                return f"CRITICAL BLUNDER! Turned an equal position into a losing one ({state_change_desc}). CPL: {cpl} ({cpl/100:.1f} pawns). {best_move} kept the balance."
            else:
                return f"Critical blunder with significant state change ({state_change_desc}). CPL: {cpl} ({cpl/100:.1f} pawns). {best_move} was much better."
        else:
            # High CPL but no critical state change
            if cpl >= 500:
                return f"Major inaccuracy, but game outcome unchanged. You're still in a {state_after.value} position. CPL: {cpl} ({cpl/100:.1f} pawns). {best_move} was more accurate."
            else:
                return f"Inaccuracy noted, but position remains {state_after.value}. CPL: {cpl} ({cpl/100:.1f} pawns). {best_move} was better."

    def _classify_game_state(self, evaluation_cp: int) -> GameState:
        """
        Classify the game state based on evaluation in centipawns.

        Args:
            evaluation_cp: Evaluation in centipawns

        Returns:
            GameState enum value (WINNING, EQUAL, or LOSING)
        """
        # Convert centipawns to pawns
        evaluation_pawns = evaluation_cp / 100.0

        if abs(evaluation_pawns) <= self.equal_state_threshold:
            return GameState.EQUAL
        elif evaluation_pawns >= self.winning_state_threshold:
            return GameState.WINNING
        elif evaluation_pawns <= -self.winning_state_threshold:
            return GameState.LOSING
        elif evaluation_pawns > 0:
            return GameState.WINNING  # Slightly winning
        else:
            return GameState.LOSING   # Slightly losing

    def _is_negative_state_change(self, before_state: GameState, after_state: GameState) -> bool:
        """
        Determine if a state change is negative (worsens the position).

        Args:
            before_state: Game state before the move
            after_state: Game state after the move

        Returns:
            True if the state change is negative, False otherwise
        """
        # Define state hierarchy: WINNING > EQUAL > LOSING
        state_values = {
            GameState.WINNING: 2,
            GameState.EQUAL: 1,
            GameState.LOSING: 0
        }

        return state_values[after_state] < state_values[before_state]

# Convenience function for backward compatibility and ease of use
def is_critical_moment(analysis_data: Dict[str, Any], tactic_threshold: int = None) -> bool:
    """
    Convenience function to check if a position is a critical moment.
    
    Args:
        analysis_data: Analysis data from the chess engine
        tactic_threshold: Optional threshold override
    
    Returns:
        True if this is a critical moment, False otherwise
    """
    brain = TutorBrain(tactic_threshold)
    return brain.is_critical_moment(analysis_data)

def analyze_critical_moment(analysis_data: Dict[str, Any], tactic_threshold: int = None) -> Optional[Dict[str, Any]]:
    """
    Convenience function to analyze a critical moment.

    Args:
        analysis_data: Analysis data from the chess engine
        tactic_threshold: Optional threshold override

    Returns:
        Critical moment analysis or None if not critical
    """
    brain = TutorBrain(tactic_threshold)
    return brain.analyze_critical_moment(analysis_data)

def analyze_user_move(pre_move_analysis: Dict[str, Any], user_move_uci: str, blunder_threshold: int = None) -> Dict[str, Any]:
    """
    Convenience function to analyze a user's move for blunders.

    Args:
        pre_move_analysis: Analysis data from before the user's move
        user_move_uci: The move the user played in UCI format
        blunder_threshold: Optional threshold override

    Returns:
        Blunder analysis dictionary
    """
    brain = TutorBrain(blunder_threshold=blunder_threshold)
    return brain.analyze_user_move(pre_move_analysis, user_move_uci)
