"""
Global hotkey manager for the Guardian Chess Tutor Desktop Client.

This module provides system-wide keyboard shortcuts for overlay control.
"""

import logging
from typing import Optional, Callable
from PySide6.QtCore import QObject, Signal
from pynput import keyboard
from pynput.keyboard import GlobalHotKeys

logger = logging.getLogger(__name__)


class HotkeyManager(QObject):
    """
    Manages global hotkeys for overlay control.
    
    Provides system-wide keyboard shortcuts that work even when the application
    is not in focus, allowing users to toggle overlay mode from anywhere.
    """
    
    # Signal for hotkey events
    toggle_overlay_requested = Signal()
    
    def __init__(self, parent: Optional[QObject] = None):
        """
        Initialize the hotkey manager.
        
        Args:
            parent: Parent QObject (optional)
        """
        super().__init__(parent)
        
        self.hotkeys: Optional[GlobalHotKeys] = None
        self.enabled = False
        
        # Default hotkey combination
        self.hotkey_combinations = {
            '<ctrl>+<alt>+g': self._on_toggle_overlay,
        }
        
        logger.info("Hotkey manager initialized")
    
    def start(self) -> bool:
        """
        Start listening for global hotkeys.
        
        Returns:
            True if hotkeys were successfully registered, False otherwise
        """
        try:
            if self.hotkeys:
                self.stop()
            
            logger.info("Starting global hotkey listener")
            logger.info("Hotkey combination:")
            logger.info("  Ctrl+Alt+G: Toggle overlay mode")
            
            self.hotkeys = GlobalHotKeys(self.hotkey_combinations)
            self.hotkeys.start()
            self.enabled = True
            
            logger.info("Global hotkeys registered successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start global hotkeys: {e}")
            self.enabled = False
            return False
    
    def stop(self) -> None:
        """Stop listening for global hotkeys."""
        try:
            if self.hotkeys:
                logger.info("Stopping global hotkey listener")
                self.hotkeys.stop()
                self.hotkeys = None
                self.enabled = False
                logger.info("Global hotkeys stopped")
        except Exception as e:
            logger.error(f"Failed to stop global hotkeys: {e}")
    
    def is_enabled(self) -> bool:
        """
        Check if hotkeys are currently enabled.
        
        Returns:
            True if hotkeys are active, False otherwise
        """
        return self.enabled
    
    def _on_toggle_overlay(self) -> None:
        """Handle toggle overlay hotkey."""
        logger.info("Toggle overlay hotkey pressed (Ctrl+Alt+G)")
        self.toggle_overlay_requested.emit()
    
    def get_hotkey_info(self) -> dict:
        """
        Get information about registered hotkeys.

        Returns:
            Dictionary with hotkey information
        """
        return {
            'toggle_overlay': 'Ctrl+Alt+G',
            'enabled': self.enabled
        }
