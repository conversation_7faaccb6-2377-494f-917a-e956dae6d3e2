### Introduction

This guide outlines a phased approach to building your chess assistant. We will start with the most reliable method (Lichess API), move to a more complex browser extension for Chess.com, and finally cover the screen-capture fallback. At the core of all phases is a sophisticated "Decider" module that identifies important moments in the game.

### Core Component: The "Important Moment" Decider

This module is the brain of your assistant. It runs in the background and decides when to provide a hint. It will use a chess engine like Stockfish.js configured for **Multi-PV (Multi-Principal Variation) analysis**. Instead of just finding the single best move, we'll ask the engine for the top 3 best moves and their evaluations.

Here is the refined logic:

**1\. Tactic Finder Logic (Pre-Move Analysis):**

* **Trigger:** It's your turn to move.  
* **Action:**  
  1. Get the current board state (FEN).  
  2. Ask the engine to calculate the top 3 moves (MultiPV 3).  
  3. Let the evaluation of the best move be E1​.  
  4. **Condition:** If E1​ is significantly better than the current board evaluation *before* your turn (e.g., a jump of \+2.0 pawns or more), it means a winning tactic is available.  
  5. **Result:** Alert the user that a tactical opportunity exists and hint at the best move.

**2\. Critical Point Analysis Logic (Post-Move Analysis):**

* **Trigger:** You have just made your move.  
* **Action:**  
  1. Get the FEN of the position *before* your move.  
  2. Ask the engine to calculate the top 3 moves (MultiPV 3). Let the evaluations be E1​, E2​, and E3​.  
  3. **Volatility Check:** Calculate the difference between the best move and the third-best move: ΔE=∣E1​−E3​∣. If ΔE\>1.5 (a high threshold), the position was critical.  
  4. **Blunder Check:** Compare the evaluation of the move you *actually* played (Euser​) with the best move's evaluation (E1​). If E1​−Euser​\>0.8 (a significant drop), you missed the best move in a critical moment.  
  5. **Result:** Alert the user that they were at a critical point and may have blundered, then suggest the better move.

### Phase 1: Lichess Integration (API-Based)

This is the ideal starting point due to Lichess's excellent, free API. We will build this as a **browser extension** (e.g., for Chrome/Firefox) for a seamless experience.

#### Architecture:

graph TD  
    A\[User plays on Lichess.org\] \--\> B{Browser Extension};  
    B \--\> C\[Lichess API Stream\];  
    C \-- Game Events \--\> D\[Background Script\];  
    D \-- Board FEN \--\> E{Chess Engine (Stockfish.js)};  
    E \-- Top 3 Moves & Evals \--\> D;  
    D \--\> F\[Decider Logic\];  
    F \-- Is it an important moment? \--\> G{Decision};  
    G \-- Yes \--\> H\[Content Script\];  
    H \-- Injects UI element \--\> A;

#### Implementation Steps:

1. **Set up a Lichess API Token:** You'll need to register an account on Lichess and create a Personal API Token with Read incoming events scope.  
2. **Create a Browser Extension:**  
   * **manifest.json:** Defines the extension, its permissions (storage, access to lichess.org), and its components.  
   * **background.js (Background Script):** This script runs in the background. It will be responsible for connecting to the Lichess event stream (https://lichess.org/api/stream/event) using your API token. It listens for game updates.  
   * **content.js (Content Script):** This script is injected directly into the Lichess webpage. It can modify the page, for example, by adding a small floating window for your assistant's suggestions or highlighting squares.  
3. **Develop the Logic (background.js):**  
   * When a game starts, the event stream will send the full game state, including the FEN.  
   * For each move, the stream sends the move in UCI format (e.g., e2e4). Use the chess.js library to update your board state and generate a new FEN.  
   * Feed this FEN to the "Decider" module (which also runs in the background script).  
   * If the Decider flags an important moment, the background script sends a message to the content script.  
4. **Develop the UI (content.js):**  
   * The content script listens for messages from the background script.  
   * When it receives a "suggestion" message, it creates and displays a visual element on the page (e.g., a simple \<div\> with the suggested move).

### Phase 2: Chess.com Integration (DOM Scraping)

Chess.com's API is not suitable for real-time game analysis, so we must adapt our browser extension to "read" the board directly from the web page.

#### Architecture:

The architecture is similar, but the data source changes. Instead of an API stream, the Content Script actively scrapes the page.

graph TD  
    A\[User plays on Chess.com\] \--\> B{Browser Extension};  
    B \--\> C\[Content Script\];  
    C \-- Reads move list from page HTML \--\> D\[Background Script\];  
    D \-- Board FEN \--\> E{Chess Engine (Stockfish.js)};  
    E \-- Top 3 Moves & Evals \--\> D;  
    D \--\> F\[Decider Logic\];  
    F \-- Is it an important moment? \--\> G{Decision};  
    G \-- Yes \--\> C;  
    C \-- Injects UI element \--\> A;

#### Implementation Steps:

1. **Adapt the Content Script (content.js):**  
   * This is the most critical change. You need to write JavaScript that can find the element on the Chess.com page that contains the list of moves (the PGN). This requires using browser developer tools to inspect the HTML structure of a live game page.  
   * Use a MutationObserver to detect when a new move is added to the list.  
   * When a new move appears, send it to your background script.  
2. **Adapt the Background Script (background.js):**  
   * The core logic remains the same. It takes moves from the content script (instead of the Lichess API), updates its chess.js instance, runs the Decider, and sends back suggestions.  
3. **Challenges:** This method is more fragile. If Chess.com updates its website's HTML structure, your content script will break and need to be updated.

### Phase 3: Screen-Capture Fallback (Desktop App)

This is the most complex phase and should be treated as a separate, advanced project. It would require building a desktop application.

#### Technology Stack:

* **Application Framework:** **Electron.js** is a good choice, as it lets you build cross-platform desktop apps using web technologies (JavaScript, HTML, CSS).  
* **Screen Capture:** Electron has built-in APIs (desktopCapturer) to get screen sources.  
* **Computer Vision:** **OpenCV.js** is the JavaScript binding for OpenCV, the leading computer vision library.

#### Architecture & Workflow:

1. **Capture:** The user selects the screen area containing the chessboard. The app takes periodic screenshots.  
2. **Board Detection:** Use OpenCV to find the boundaries of the 8x8 grid in the captured image. This involves techniques like edge detection (Canny) and contour finding.  
3. **Square Segmentation:** Once the board is found, mathematically divide its coordinates into 64 individual squares.  
4. **Piece Recognition:** This is the hardest step. For each square, you must classify which piece is on it (or if it's empty). This typically requires a simple machine learning model (or advanced template matching) that you train by feeding it thousands of images of different pieces (King, Queen, etc.) from different chess sets.  
5. **FEN Reconstruction:** After classifying all 64 squares, assemble the board state into a FEN string.  
6. **Analysis:** Feed the reconstructed FEN to the same "Decider" logic from the previous phases.  
7. **Display:** Show the suggestion in a separate desktop window.

This phase is a significant undertaking, but it provides a universal solution that can, in theory, work with any chess application. Start with the browser-based approaches first, as they deliver 90% of the value for 20% of the effort.