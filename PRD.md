### Product Requirements Document: "Guardian" Chess Tutor

Version: 1.0  
Date: June 23, 2025  
Author: <PERSON> (Lead Product & Technical Strategist)  
Status: In Review

### 1\. Introduction & Vision

1.1. Vision Statement  
To create a next-generation chess training assistant that accelerates player improvement by providing real-time, context-aware guidance during live games. <PERSON> acts as a silent partner, intervening only at critical pedagogical moments to help users recognize opportunities and avoid game-altering mistakes, fostering deep learning without creating a dependency on the engine.  
1.2. Problem Statement  
Beginner and intermediate chess players (sub-1500 Elo) often struggle to identify key tactical opportunities and critical defensive moves during a live game. Post-game analysis is valuable but lacks the immediacy needed to correct recurring errors in judgment. Existing real-time tools often function as simple "cheating" applications, providing constant engine-best moves that hinder, rather than help, genuine skill development. There is a clear market gap for a tool that bridges live play and effective learning.  
1.3. Core Concept: The "Alternating-Move" Protocol  
Guardian is built around a novel interactive learning model. The system operates in one of two modes:

* **User Move Mode (Default):** The user has full autonomy to play their own moves. The system analyzes silently in the background.  
* **Guardian Move Mode (Triggered):** When the system detects a "Critical Moment," it intervenes. It will suggest the optimal move and require the user to play it, ensuring they internalize the correct pattern. Immediately after, the system returns to User Move Mode.

### 2\. Goals & Objectives

| Goal | Metric |
| :---- | :---- |
| **Improve Player Skill** | Increase in users' average online chess rating over a 3-month period. |
|  | Decrease in the frequency of user-flagged "blunder" types after 50 games. |
| **Provide Actionable Insights** | High user engagement with "Guardian Move" suggestions (low skip/ignore rate). |
| **Achieve Broad Reach** | Successful integration with Lichess (Phase 1\) and Chess.com (Phase 2). |
| **Ensure System Viability** | Achieve \>99% FEN reconstruction accuracy for the universal CV model on target themes. |

### 3\. Target Audience & Personas

* **Primary Persona: "The Ambitious Novice" (Elo 600-1200)**  
  * **Description:** Plays chess regularly online. Understands the basic rules but struggles with tactical awareness, positional understanding, and avoiding one-move blunders.  
  * **Needs:** Wants to improve but finds raw engine analysis intimidating. Needs immediate, contextual feedback to understand *why* a move is good or bad.  
  * **Frustrations:** Loses games due to simple mistakes they only recognize after the fact. Feels "stuck" at their current rating.

### 4\. System Architecture & Non-Functional Requirements

4.1. High-Level Architecture  
The system will be built on a decoupled, multi-process architecture as detailed in the research. The core components are:

1. **Game State Acquisition Module:** Interfaces with external platforms (API, DOM, or CV).  
2. **State Management Module:** Tracks game history and infers state.  
3. **Engine Interaction Module:** Manages the Stockfish UCI process.  
4. **Logic Module ("Tutor Brain"):** Houses the "Critical Moment" algorithms.  
5. **UI Overlay Module:** Renders suggestions to the user.

**4.2. Non-Functional Requirements**

* **Performance:** End-to-end analysis (from move played to suggestion displayed) must complete in under 3 seconds to feel responsive.  
* **Accuracy:** The system's internal board state must remain perfectly synchronized with the user's actual game. For the CV module, piece recognition accuracy must exceed 99% on supported themes.  
* **Reliability:** The system must handle network interruptions and API changes gracefully, with automatic reconnection logic.  
* **Security:** User authentication tokens (Lichess PAT) must be stored securely on the client machine.  
* **Usability:** The UI overlay must be minimalist, non-intrusive, and completely invisible when not active.

### 5\. Phased Rollout & Feature Requirements

The project will be developed in three distinct phases to manage technical risk and deliver value iteratively.

#### Phase 1: MVP \- The Lichess Specialist

**Objective:** Validate the core analysis engine and learning loop in a stable, controlled environment.

| Epic | User Story / Requirement | Technical Notes |
| :---- | :---- | :---- |
| **Backend Analysis Engine** | As a developer, I need a backend service that can receive a FEN string and analyze it with Stockfish. | \- Use Python. \- Integrate native Stockfish via the UCI protocol using the python-chess library. \- Host as a server-side process for maximum performance. |
| **"Tutor Brain" Logic** | As a player, I want to be notified only at critical moments in the game. | \- Implement the "Tactic Finder" and "Blunder Detection" algorithms using Multi-PV analysis (MultiPV=5). \- Use the contextual thresholds from the research (e.g., CRITICAL\_SWING\_CP \= 200, BALANCED\_STATE\_RANGE\_CP \= \[-100, 100\]). |
| **Lichess Integration** | As a player, I want to connect my Lichess account to Guardian so it can follow my live games. | \- Use the berserk library to connect to the Lichess Board API. \- Implement authentication using a user-provided Personal Access Token (PAT). \- Listen to /api/stream/event for gameStart and then /api/board/game/stream/{gameId} for real-time state. |
| **Client & UI Overlay** | As a player, when Guardian detects a critical moment, I want to see a clear visual suggestion on my screen. | \- Develop a minimal desktop client (e.g., using PyQt/PySide). \- Implement a transparent, "always-on-top" overlay window. \- When a suggestion is triggered, the overlay should draw a simple arrow from the piece's start square to its end square. |

#### Phase 2: V2 \- The Chess.com Browser Extension

**Objective:** Extend the system's reach to the largest online chess platform.

| Epic | User Story / Requirement | Technical Notes |
| :---- | :---- | :---- |
| **Browser Extension** | As a player on Chess.com, I want to install a browser extension that enables Guardian to analyze my games. | \- Develop a Chrome Extension using the Manifest V3 standard. \- Create a background service worker to communicate with the existing Phase 1 backend. |
| **DOM Scraping** | The extension must accurately detect moves as they are played on Chess.com. | \- Implement a content script injected into Chess.com game pages. \- Use the MutationObserver API to monitor the move list container for changes. \- Develop a robust selector strategy to identify the move list, making it as resilient as possible to UI updates. |
| **Ethical & ToS Handling** | As a user, I must be informed of the risks of using a third-party tool on Chess.com. | \- The extension's UI must include a clear disclaimer about potential Terms of Service violations and anti-cheating detection. \- The extension must operate as passively as possible to minimize detection risk. |

#### Phase 3: V3 \- The Universal Vision App

**Objective:** Create a platform-agnostic solution that can work with any chess application.

| Epic | User Story / Requirement | Technical Notes |
| :---- | :---- | :---- |
| **Desktop Application** | As a player, I want a single desktop application that can analyze my game, no matter what website or program I'm using. | \- Develop a cross-platform desktop application using the Electron.js framework. \- The UI should be built with HTML/CSS/JS. |
| **Computer Vision Pipeline** | The application must be able to "see" and understand the chessboard on my screen. | \- Implement the full CV pipeline: screen capture, board localization (OpenCV), and perspective transformation. \- The user must be able to select the screen region containing the board during a one-time setup. |
| **Piece Recognition (CNN)** | The CV pipeline must accurately identify all the pieces on the board. | \- Develop and train a Convolutional Neural Network (CNN) using PyTorch or TensorFlow. \- MVP Target: The initial model must achieve \>99% accuracy on the default board/piece themes of Lichess and Chess.com. \- The output of the CNN must be converted into a valid FEN string. |
| **State Inference** | The application must correctly infer game state details not visible in a single image. | \- If castling rights or en-passant cannot be inferred by comparing consecutive frames, the user must be prompted to provide this information manually during setup. |

### 6\. Risks & Mitigation

| Risk | Severity | Mitigation Strategy |
| :---- | :---- | :---- |
| **CV Model Brittleness** | **High** | Strictly adhere to the phased rollout. The Phase 3 MVP will only support a limited set of popular, default themes. Future work will involve building a larger, more diverse training dataset. |
| **Chess.com DOM Breakage** | **High** | Acknowledge that this is an unavoidable maintenance cost. Dedicate engineering resources to monitor Chess.com updates and reactively patch the extension's selectors. |
| **Platform Blocking** | **Medium** | Position the product clearly as a training tool. The "Alternating-Move" protocol is a key anti-cheating feature. Prioritize Lichess, which is more open to third-party tools. |
| **Real-Time Performance** | **Medium** | Use a backend server for engine analysis. Optimize the CNN model for inference speed. Strictly cap UCI analysis time to 1-2 seconds per move. |

### 7\. Future Roadmap: The Agentic Tutor

While beyond the scope of this initial PRD, the long-term vision is to evolve the heuristic-based "Tutor Brain" into a true adaptive learning agent.

* **V4: The Personalized Tutor (Reinforcement Learning)**  
  * **Concept:** Replace the static heuristic rules with a Reinforcement Learning (RL) agent.  
  * **Action Space:** The agent's action will be to intervene or do\_nothing.  
  * **State:** The state will include not just the board FEN, but also a model of the user's skill, their historical error patterns, and gameplay metrics (e.g., time per move).  
  * **Reward:** The agent will be rewarded based on the user's long-term improvement (blunder rate reduction, rating gain).  
  * **Data Flywheel:** The data collected from the MVP and V2/V3 will be used to bootstrap the training of this RL agent.