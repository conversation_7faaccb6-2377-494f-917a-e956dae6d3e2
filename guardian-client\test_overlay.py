#!/usr/bin/env python3
"""
Test script for Guardian Chess Tutor Overlay functionality.

This script demonstrates and tests all overlay features:
- Enable/Disable overlay mode
- Transparency controls
- Global hotkeys
- Always on top functionality
- Position memory
- Click-through behavior
"""

import sys
import time
import subprocess
from pathlib import Path

def print_banner():
    """Print test banner."""
    print("=" * 60)
    print("🎯 Guardian Chess Tutor - Overlay Test Suite")
    print("=" * 60)
    print()

def print_section(title):
    """Print section header."""
    print(f"\n📋 {title}")
    print("-" * 40)

def print_test_instructions():
    """Print comprehensive test instructions."""
    print_section("OVERLAY FUNCTIONALITY TEST")
    
    print("🚀 GETTING STARTED:")
    print("1. The Guardian Client window should now be visible")
    print("2. You'll see 'Overlay Controls' section with buttons and sliders")
    print("3. Follow the test steps below to verify all features")
    print()
    
    print("🎮 TEST STEPS:")
    print()
    
    print("Step 1: Basic Overlay Toggle")
    print("  • Click the 'Enable Overlay Mode' button")
    print("  • Window becomes borderless, transparent, and always on top")
    print("  • Most UI elements hide, only toggle button remains visible")
    print("  • Status shows '👻 Ghost Mode Active'")
    print("  • Button changes to 'Disable Overlay Mode' (red)")
    print("  • Click the button again to return to normal mode")
    print()

    print("Step 2: Global Hotkey (System-wide)")
    print("  • Press Ctrl+Alt+G to toggle overlay mode on/off")
    print("  • Works from any application, even when Guardian is not focused!")
    print("  • Try it while Guardian is in overlay mode")
    print("  • Try it while Guardian is in normal mode")
    print()

    print("Step 3: Transparency Control")
    print("  • Use the opacity slider (10% - 100%) in normal mode")
    print("  • Enable overlay mode to see the transparency effect")
    print("  • Lower values = more transparent")
    print("  • Transparency persists in overlay mode")
    print()

    print("Step 4: Always On Top Behavior")
    print("  • In overlay mode, window automatically stays on top")
    print("  • No manual setting needed - it's automatic!")
    print("  • Test by opening other applications")
    print()

    print("Step 5: Position Memory")
    print("  • Move the window to different positions")
    print("  • Close and restart the application")
    print("  • Window should remember its last position")
    print()

    print("Step 6: Edge Snapping")
    print("  • Drag window close to screen edges (in normal mode)")
    print("  • Window should 'snap' to edges when close enough")
    print("  • Works for all four edges (top, bottom, left, right)")
    print()
    
    print("🎯 SUCCESS CRITERIA:")
    print("✅ Single toggle button switches between modes")
    print("✅ Global hotkey (Ctrl+Alt+G) works from any application")
    print("✅ Transparency adjusts smoothly")
    print("✅ Overlay mode is always on top automatically")
    print("✅ Toggle button remains clickable in overlay mode")
    print("✅ Position is remembered between sessions")
    print("✅ Window snaps to screen edges (in normal mode)")
    print()
    
    print("⚠️  TROUBLESHOOTING:")
    print("• If hotkeys don't work, try running as administrator")
    print("• UpdateLayeredWindowIndirect warnings are normal on Windows")
    print("• If overlay doesn't work, check Windows version (requires Vista+)")
    print()
    
    print("🏁 When testing is complete, close the Guardian window")
    print("   or press Ctrl+C in this terminal to stop the test.")

def main():
    """Run the overlay test suite."""
    print_banner()
    
    # Check if we're in the right directory
    client_dir = Path("guardian-client")
    if not client_dir.exists():
        print("❌ Error: guardian-client directory not found!")
        print("   Please run this script from the Guardian project root directory.")
        return 1
    
    print("🔧 Starting Guardian Chess Tutor with Overlay functionality...")
    print()
    
    try:
        # Change to client directory and start the application
        import os
        os.chdir(client_dir)
        
        # Print test instructions
        print_test_instructions()
        
        # Start the Guardian Client
        print("\n🚀 Launching Guardian Client...")
        print("   (Close the application window when testing is complete)")
        print()
        
        # Run the client
        result = subprocess.run([
            "venv\\Scripts\\python.exe", "-m", "guardian_client"
        ], cwd=".")
        
        print("\n✅ Guardian Client closed.")
        print("🎉 Overlay testing complete!")
        
        return result.returncode
        
    except KeyboardInterrupt:
        print("\n\n⏹️  Test interrupted by user.")
        return 0
    except Exception as e:
        print(f"\n❌ Error running test: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
