[tool.poetry]
name = "guardian-engine"
version = "0.1.0"
description = "Guardian Chess Tutor Backend Analysis Engine"
authors = ["Guardian Team"]
readme = "README.md"
packages = [{include = "guardian_engine"}]

[tool.poetry.dependencies]
python = "^3.8.1"
flask = "^3.0.0"
python-chess = "^1.999"
berserk = "^0.13.2"

[tool.poetry.group.dev.dependencies]
pytest = "^7.0.0"
black = "^23.0.0"
flake8 = "^6.0.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.poetry.scripts]
guardian-engine = "guardian_engine.app:main"
