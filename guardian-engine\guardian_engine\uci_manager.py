"""
UCI Manager for Guardian Chess Tutor Backend.

Handles communication with Stockfish chess engine via the Universal Chess Interface (UCI) protocol.
"""

import logging
import chess
import chess.engine
from pathlib import Path
from typing import Optional, Dict, Any, Union

logger = logging.getLogger(__name__)

class UCIManagerError(Exception):
    """Custom exception for UCI Manager errors."""
    pass

class UCIManager:
    """
    Manages UCI communication with Stockfish chess engine.
    
    This class handles:
    - Engine process lifecycle
    - UCI protocol handshake
    - Engine configuration
    - Error handling and logging
    """
    
    def __init__(self, stockfish_path: str, timeout: int = 30):
        """
        Initialize UCI Manager.
        
        Args:
            stockfish_path: Path to Stockfish executable
            timeout: Timeout for UCI operations in seconds
        """
        self.stockfish_path = stockfish_path
        self.timeout = timeout
        self.engine: Optional[chess.engine.SimpleEngine] = None
        self.engine_info: Dict[str, Any] = {}
        
        logger.info(f"Initializing UCI Manager with Stockfish path: {stockfish_path}")
        self._initialize_engine()
    
    def _initialize_engine(self):
        """Initialize the Stockfish engine and perform UCI handshake."""
        try:
            # Verify Stockfish executable exists
            if not self._verify_stockfish_path():
                raise UCIManagerError(f"Stockfish executable not found at: {self.stockfish_path}")
            
            # Start the engine
            logger.info("Starting Stockfish engine...")
            self.engine = chess.engine.SimpleEngine.popen_uci(
                self.stockfish_path,
                timeout=self.timeout
            )
            
            # Get engine information
            self._collect_engine_info()
            
            logger.info(f"UCI handshake successful with {self.engine_info.get('name', 'Unknown Engine')}")
            
        except chess.engine.EngineError as e:
            logger.error(f"Engine error during initialization: {e}")
            raise UCIManagerError(f"Failed to initialize engine: {e}")
        except Exception as e:
            logger.error(f"Unexpected error during engine initialization: {e}")
            raise UCIManagerError(f"Unexpected error: {e}")
    
    def _verify_stockfish_path(self) -> bool:
        """
        Verify that the Stockfish executable exists and is accessible.
        
        Returns:
            True if path is valid, False otherwise
        """
        try:
            # Check if it's just 'stockfish' (in PATH)
            if self.stockfish_path in ['stockfish', 'stockfish.exe']:
                return True
            
            # Check if file exists
            path = Path(self.stockfish_path)
            if not path.exists():
                logger.error(f"Stockfish executable not found: {self.stockfish_path}")
                return False
            
            if not path.is_file():
                logger.error(f"Stockfish path is not a file: {self.stockfish_path}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error verifying Stockfish path: {e}")
            return False
    
    def _collect_engine_info(self):
        """Collect engine information from UCI handshake."""
        if not self.engine:
            return
        
        try:
            # Get engine ID information
            self.engine_info = {
                'name': getattr(self.engine, 'id', {}).get('name', 'Unknown'),
                'author': getattr(self.engine, 'id', {}).get('author', 'Unknown'),
                'path': self.stockfish_path,
                'options': {}
            }
            
            # Get available options
            if hasattr(self.engine, 'options'):
                for option_name, option in self.engine.options.items():
                    self.engine_info['options'][option_name] = {
                        'type': option.type,
                        'default': option.default,
                        'min': getattr(option, 'min', None),
                        'max': getattr(option, 'max', None),
                        'var': getattr(option, 'var', None)
                    }
            
            logger.info(f"Collected engine info: {self.engine_info['name']} by {self.engine_info['author']}")
            
        except Exception as e:
            logger.warning(f"Could not collect complete engine info: {e}")
            self.engine_info = {
                'name': 'Stockfish',
                'author': 'Unknown',
                'path': self.stockfish_path,
                'options': {}
            }
    
    def get_engine_info(self) -> Dict[str, Any]:
        """
        Get engine information.
        
        Returns:
            Dictionary containing engine information
        """
        return self.engine_info.copy()
    
    def is_engine_ready(self) -> bool:
        """
        Check if engine is ready for commands.

        Returns:
            True if engine is ready, False otherwise
        """
        return self.engine is not None

    def analyze_position(self, fen: str, time_limit: float = 2.0, multi_pv: int = 1) -> Dict[str, Any]:
        """
        Analyze a chess position and return the best move(s).

        Args:
            fen: FEN string representing the position
            time_limit: Analysis time in seconds (default: 2.0)
            multi_pv: Number of principal variations to analyze (default: 1)

        Returns:
            Dictionary containing analysis results

        Raises:
            UCIManagerError: If engine is not ready or analysis fails
        """
        if not self.is_engine_ready():
            raise UCIManagerError("Engine is not ready for analysis")

        try:
            # Validate and parse the FEN string
            board = chess.Board(fen)
            logger.info(f"Analyzing position: {fen} with MultiPV={multi_pv}")

            # Perform analysis using python-chess engine interface
            limit = chess.engine.Limit(time=time_limit)

            # Use the multipv parameter directly in analyse() - python-chess handles MultiPV automatically
            if multi_pv == 1:
                # Single PV analysis (backward compatibility)
                result = self.engine.analyse(board, limit)

                # Extract the best move
                best_move = None
                if 'pv' in result and result['pv']:
                    best_move = result['pv'][0]  # First move in principal variation

                # Format the response for single PV
                analysis_result = {
                    'bestMove': str(best_move) if best_move else None,
                    'evaluation': self._format_evaluation(result.get('score')),
                    'depth': result.get('depth', 0),
                    'time': time_limit,
                    'fen': fen,
                    'analysis': [{
                        'move': str(best_move) if best_move else None,
                        'evaluation': self._format_evaluation(result.get('score')),
                        'depth': result.get('depth', 0),
                        'pv': [str(move) for move in result.get('pv', [])]
                    }] if best_move else []
                }
            else:
                # Multi-PV analysis - create fresh engine instance for MultiPV
                # This avoids the "MultiPV automatically managed" error
                with chess.engine.SimpleEngine.popen_uci(self.stockfish_path, timeout=self.timeout) as temp_engine:
                    results = temp_engine.analyse(board, limit, multipv=multi_pv)

                    # Process multiple variations
                    analysis_list = []
                    best_move = None

                    for i, result in enumerate(results):
                        if 'pv' in result and result['pv']:
                            move = result['pv'][0]
                            if i == 0:  # First variation is the best move
                                best_move = move

                            analysis_list.append({
                                'move': str(move),
                                'evaluation': self._format_evaluation(result.get('score')),
                                'depth': result.get('depth', 0),
                                'pv': [str(m) for m in result.get('pv', [])],
                                'multipv': i + 1
                            })

                    # Format the response for multi-PV
                    analysis_result = {
                        'bestMove': str(best_move) if best_move else None,
                        'evaluation': self._format_evaluation(results[0].get('score')) if results else None,
                        'depth': results[0].get('depth', 0) if results else 0,
                        'time': time_limit,
                        'fen': fen,
                        'multiPv': multi_pv,
                        'analysis': analysis_list
                    }

            logger.info(f"Analysis complete: best move = {best_move}")
            return analysis_result

        except ValueError as e:
            # chess.Board() raises ValueError for invalid FEN in newer versions
            logger.error(f"Invalid FEN string: {fen} - {e}")
            raise UCIManagerError(f"Invalid FEN string: {e}")
        except chess.engine.EngineError as e:
            logger.error(f"Engine error during analysis: {e}")
            raise UCIManagerError(f"Engine analysis failed: {e}")
        except Exception as e:
            logger.error(f"Unexpected error during analysis: {e}")
            raise UCIManagerError(f"Analysis failed: {e}")

    def _format_evaluation(self, score) -> Optional[Dict[str, Any]]:
        """
        Format the engine evaluation score.

        Args:
            score: Chess engine score object

        Returns:
            Formatted evaluation dictionary or None
        """
        if not score:
            return None

        try:
            # Handle different score types from python-chess
            if hasattr(score, 'is_mate') and score.is_mate():
                # Mate score
                mate_value = score.mate() if hasattr(score, 'mate') else getattr(score, 'moves', 0)
                return {
                    'type': 'mate',
                    'value': mate_value,
                    'description': f"Mate in {abs(mate_value)}"
                }
            else:
                # Centipawn score
                cp_value = None

                # Try different ways to get centipawn value
                if hasattr(score, 'relative'):
                    # PovScore with relative attribute
                    if hasattr(score.relative, 'score'):
                        cp_value = score.relative.score(mate_score=10000)
                    elif hasattr(score.relative, 'cp'):
                        cp_value = score.relative.cp
                elif hasattr(score, 'score'):
                    # Direct score method
                    cp_value = score.score(mate_score=10000)
                elif hasattr(score, 'cp'):
                    # Direct centipawn attribute
                    cp_value = score.cp

                if cp_value is not None:
                    return {
                        'type': 'centipawns',
                        'value': cp_value,
                        'description': f"{cp_value/100:.2f} pawns"
                    }
                else:
                    logger.warning(f"Could not extract centipawn value from score: {type(score)}")
                    return {
                        'type': 'unknown',
                        'value': 0,
                        'description': 'Evaluation unavailable'
                    }

        except Exception as e:
            logger.warning(f"Could not format evaluation score: {e}")
            logger.debug(f"Score object type: {type(score)}, attributes: {dir(score) if score else 'None'}")
            return {
                'type': 'unknown',
                'value': 0,
                'description': 'Evaluation unavailable'
            }
    
    def quit(self):
        """Properly shutdown the engine."""
        if self.engine:
            try:
                logger.info("Shutting down Stockfish engine...")
                self.engine.quit()
                self.engine = None
                logger.info("Engine shutdown complete")
            except Exception as e:
                logger.error(f"Error during engine shutdown: {e}")
    
    def __enter__(self):
        """Context manager entry."""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.quit()
    
    def __del__(self):
        """Destructor to ensure engine is properly closed."""
        self.quit()
